/**
 * pupcdp_framemanager.js - Simplified CDP implementation following FrameManager pattern
 *
 * This implementation follows the key principles from Puppeteer's FrameManager:
 * 1. Use Target.setAutoAttach with flatten: true
 * 2. Handle Target.attachedToTarget events for iframe sessions
 * 3. Inject scripts into all sessions (main + iframe sessions)
 * 4. Use the correct CDP session management
 */

const puppeteer = require("puppeteer");

(async () => {
  // 1) Launch Chrome with remote-debugging enabled
  const browser = await puppeteer.launch({
    headless: false,
    args: ["--remote-debugging-port=9222"],
    defaultViewport: null,
  });

  // 2) Grab the WebSocket URL from Chrome's /json/version
  const versionJson = await fetch("http://localhost:9223/json/version").then(
    (r) => r.json()
  );
  const { webSocketDebuggerUrl } = versionJson;

  // 3) Connect Puppeteer to that WS endpoint
  const connected = await puppeteer.connect({
    browserWSEndpoint: webSocketDebuggerUrl,
  });

  // 4) Open a new blank page and get its "main" CDP session
  const page = await connected.newPage();
  const mainSession = await page.createCDPSession();

  // 5) The injection script
  const scriptSource = 'console.log("hii from new page", document.URL);';
  const WORLD_NAME = "custom-world";

  // Track all sessions that we need to inject scripts into
  const allSessions = new Set();
  allSessions.add(mainSession);

  // Function to inject script into a session (mimicking FrameManager approach)
  async function injectScriptIntoSession(session, sessionId = null) {
    const id = sessionId || session.id();
    console.log(`Injecting script into session: ${id}`);

    try {
      // Enable Page and Runtime for this session
      await session.send("Page.enable");
      await session.send("Runtime.enable");

      // Add script to evaluate on new document
      await session.send("Page.addScriptToEvaluateOnNewDocument", {
        source: scriptSource,
        worldName: WORLD_NAME,
      });

      // Get current frame tree and create isolated worlds
      const { frameTree } = await session.send("Page.getFrameTree");
      await processFrameTreeForSession(session, frameTree);

      console.log(`✓ Successfully injected script into session: ${id}`);
    } catch (error) {
      console.error(
        `Failed to inject script into session ${id}:`,
        error.message
      );
    }
  }

  // Process frame tree and create isolated worlds
  async function processFrameTreeForSession(session, frameTree) {
    try {
      await session.send("Page.createIsolatedWorld", {
        frameId: frameTree.frame.id,
        worldName: WORLD_NAME,
        grantUniversalAccess: true,
      });
      console.log(`Created isolated world for frame: ${frameTree.frame.id}`);
    } catch (error) {
      console.error(
        `Failed to create isolated world for frame ${frameTree.frame.id}:`,
        error.message
      );
    }

    // Process child frames recursively
    if (frameTree.childFrames) {
      for (const childFrame of frameTree.childFrames) {
        await processFrameTreeForSession(session, childFrame);
      }
    }
  }

  // Setup event listeners for frame events
  function setupFrameEventListeners(session) {
    session.on("Page.frameNavigated", async (event) => {
      const { frame } = event;
      console.log(`Frame navigated: ${frame.id} -> ${frame.url}`);

      // Create isolated world for the navigated frame
      try {
        await session.send("Page.createIsolatedWorld", {
          frameId: frame.id,
          worldName: WORLD_NAME,
          grantUniversalAccess: true,
        });
        console.log(`Created isolated world for navigated frame: ${frame.id}`);
      } catch (error) {
        console.error(
          `Failed to create isolated world for navigated frame:`,
          error.message
        );
      }
    });
  }

  // Initialize main session
  console.log("→ Setting up main session...");
  setupFrameEventListeners(mainSession);
  await injectScriptIntoSession(mainSession);

  // Setup autoAttach for iframe targets (this is the key part from FrameManager)
  console.log("→ Setting up autoAttach for iframe targets...");
  await mainSession.send("Target.setAutoAttach", {
    autoAttach: true,
    waitForDebuggerOnStart: false,
    flatten: true, // This is crucial - it flattens the session hierarchy
  });

  // Handle new iframe targets (this mimics FrameManager.onAttachedToTarget)
  mainSession.on("Target.attachedToTarget", async (event) => {
    const { targetInfo, sessionId } = event;
    console.log(
      `Target attached: ${targetInfo.targetId} (type: ${targetInfo.type})`
    );

    if (targetInfo.type === "iframe") {
      console.log(`New iframe target detected: ${targetInfo.targetId}`);

      // With flatten: true, we can send commands directly using the sessionId
      // This is the key insight from Puppeteer's flat protocol handling
      try {
        // Enable Page and Runtime for the iframe session
        await mainSession.send("Page.enable", {}, sessionId);
        await mainSession.send("Runtime.enable", {}, sessionId);
        setupFrameEventListeners(targetInfo._session);

        // Add script to evaluate on new document for the iframe
        await mainSession.send(
          "Page.addScriptToEvaluateOnNewDocument",
          {
            source: scriptSource,
            worldName: WORLD_NAME,
          },
          sessionId
        );

        // Get frame tree for the iframe session and create isolated worlds
        const { frameTree } = await mainSession.send(
          "Page.getFrameTree",
          {},
          sessionId
        );

        // Create isolated world for the iframe's frames
        await mainSession.send(
          "Page.createIsolatedWorld",
          {
            frameId: frameTree.frame.id,
            worldName: WORLD_NAME,
            grantUniversalAccess: true,
          },
          sessionId
        );

        console.log(
          `✓ Successfully injected script into iframe session: ${sessionId}`
        );
        console.log(
          `✓ Created isolated world for iframe frame: ${frameTree.frame.id}`
        );
      } catch (error) {
        console.error(
          `Failed to inject script into iframe session ${sessionId}:`,
          error.message
        );
      }
    }
  });

  // Handle target detached events
  mainSession.on("Target.detachedFromTarget", (event) => {
    const { sessionId } = event;
    console.log(`Target detached: ${sessionId}`);
  });

  // Navigate to the test page
  console.log("→ Navigating to https://2captcha.com/demo/mtcaptcha ...");
  await mainSession.send("Page.navigate", {
    url: "https://2captcha.com/demo/mtcaptcha",
  });

  // Wait for page load
  await new Promise((resolve) =>
    mainSession.once("Page.loadEventFired", resolve)
  );

  console.log(
    "✓ Page loaded. Script should be injected in main frame and all iframes."
  );
  console.log(
    "✓ Check the browser console for 'hii from new page' messages from different frames."
  );
  console.log(
    "✓ The script should now work in iframes including MTCaptcha iframe!"
  );

  // Keep browser open for inspection
})();
