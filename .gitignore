# Session data
sessions/
cookies.json
localStorage.json
sessionStorage.json
*_proof.png
injection_proof.png
login_proof.png
logs/

# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Operating System Files
.DS_Store
Thumbs.db
thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.settings/

# Debug files
debug.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Puppeteer generated files
.local-chromium/
screenshots/

# Temporary files
tmp/
temp/