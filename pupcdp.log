2025/06/03 00:53:01 Proxy is listening for DevTools connections on: localhost:9223
^[[A00:53:07.56377700 ---------- connection from 127.0.0.1:56667 to /devtools/browser/fa26a2ee-cd99-42bc-8797-1353e82b61c7 ----------
00:53:07.56386800 checking protocol versions on: ws://localhost:9222/devtools/browser/fa26a2ee-cd99-42bc-8797-1353e82b61c7
00:53:07.56393300 Legend: protocol informations, received events, sent request frames, requests params, received responses, error response.
00:53:07.56413300 protocol version: 1.3  
00:53:07.56416300 versions: Chrome(Chrome/135.0.7049.84), V8(***********), Webkit(537.36 (@6c019e56001911b3fd467e03bf68c435924d62f4))
00:53:07.56418000 browser user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
00:53:07.56420300 connecting to ws://localhost:9222/devtools/browser/fa26a2ee-cd99-42bc-8797-1353e82b61c7... 
00:53:07.56537000 upgrading connection on 127.0.0.1:56667...
00:53:07.56650100             browser                            Target.getBrowserContexts {} => {"browserContextIds":[]}
00:53:07.56710700             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"canAccessOpener":false,"targetId":"42609358-9f27-41b8-a348-2d8e9278027d","title":"","type":"browser","url":""}}
00:53:07.56714600             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"53B51AC34C34B803823D92B48A18DF66","title":"MTCaptcha demo: Sample Form with MTCaptcha","type":"tab","url":"https://2captcha.com/demo/mtcaptcha"}}
00:53:07.56717300             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"6F452EE4245152B3714047C036286A56","title":"MTCaptcha demo: Sample Form with MTCaptcha","type":"page","url":"https://2captcha.com/demo/mtcaptcha"}}
00:53:07.56719800             browser                                 Target.targetCreated {"targetInfo":{"attached":false,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:07.56722300             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"AB59E60BFDB00743832C6BB07A0B1F19","title":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"iframe","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:07.56727400             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"C5F4FF35D2A01BDFEDA1E63318CA6197","title":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false","type":"iframe","url":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false"}}
00:53:07.56732300             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"canAccessOpener":false,"targetId":"add6e9ed-9804-4cba-a5b8-215ce59fdd1b","title":"","type":"browser","url":""}}
00:53:07.56734600             browser                            Target.setDiscoverTargets {"discover":true,"filter":[{}]} => {}
00:53:07.56851200             browser                              Target.attachedToTarget {"sessionId":"ADD2DA7171BA8178F8F0570A21EDF7BD","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"53B51AC34C34B803823D92B48A18DF66","title":"MTCaptcha demo: Sample Form with MTCaptcha","type":"tab","url":"https://2captcha.com/demo/mtcaptcha"},"waitingForDebugger":false}
00:53:07.56864000             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:07.56867600             browser                              Target.attachedToTarget {"sessionId":"11FAF61A9E66CC7FC56A673CC14CC5F0","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"},"waitingForDebugger":false}
00:53:07.56870400             browser                                 Target.setAutoAttach {"autoAttach":true,"filter":[{"exclude":true,"type":"page"},{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:07.56904100 ADD2DA7171BA8178F8F0570A21EDF7BD                 Target.attachedToTarget {"sessionId":"375C7099668BFC59D80D70C9A1626AE5","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"6F452EE4245152B3714047C036286A56","title":"MTCaptcha demo: Sample Form with MTCaptcha","type":"page","url":"https://2captcha.com/demo/mtcaptcha"},"waitingForDebugger":false}
00:53:07.56908000 ADD2DA7171BA8178F8F0570A21EDF7BD                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:07.56910200 ADD2DA7171BA8178F8F0570A21EDF7BD        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.56944800 11FAF61A9E66CC7FC56A673CC14CC5F0        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.56997900 375C7099668BFC59D80D70C9A1626AE5                 Target.attachedToTarget {"sessionId":"092F137C22A6FFE4041AAF9D439D14E5","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"C5F4FF35D2A01BDFEDA1E63318CA6197","title":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false","type":"iframe","url":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false"},"waitingForDebugger":false}
00:53:07.57007600 375C7099668BFC59D80D70C9A1626AE5                 Target.attachedToTarget {"sessionId":"EF54D1FBCE0680766CCCFC4DAA3AB301","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"AB59E60BFDB00743832C6BB07A0B1F19","title":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"iframe","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"},"waitingForDebugger":false}
00:53:07.57016500 375C7099668BFC59D80D70C9A1626AE5                 Target.attachedToTarget {"sessionId":"AF12083A575E9D9A8DE7681E4B6A7AE0","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"},"waitingForDebugger":false}
00:53:07.57026200 375C7099668BFC59D80D70C9A1626AE5                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:07.57030100 375C7099668BFC59D80D70C9A1626AE5        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.57061600             browser                            Target.detachedFromTarget {"sessionId":"11FAF61A9E66CC7FC56A673CC14CC5F0","targetId":"7C2A363B8EA8BD5060658242BE3D0A1A"}
00:53:07.57066800             browser                              Target.detachFromTarget {"sessionId":"11FAF61A9E66CC7FC56A673CC14CC5F0"} => {}
00:53:07.57108500 092F137C22A6FFE4041AAF9D439D14E5                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:07.57111400 092F137C22A6FFE4041AAF9D439D14E5        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.57141800 EF54D1FBCE0680766CCCFC4DAA3AB301                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:07.57145700 EF54D1FBCE0680766CCCFC4DAA3AB301        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.63785900             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"AF0DAF221A54C16AFBA3AA5DA55D08D1","title":"","type":"tab","url":""}}
00:53:07.63796900             browser                              Target.attachedToTarget {"sessionId":"21D161B4B26DD1D7AE000A80DD2093B2","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"AF0DAF221A54C16AFBA3AA5DA55D08D1","title":"","type":"tab","url":""},"waitingForDebugger":true}
00:53:07.63800600             browser                                 Target.targetCreated {"targetInfo":{"attached":false,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E","title":"","type":"page","url":"about:blank"}}
00:53:07.64709300             browser                                  Target.createTarget {"url":"about:blank"} => {"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E"}
00:53:07.66323600             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E","title":"","type":"page","url":"about:blank"}}
00:53:07.66357800 21D161B4B26DD1D7AE000A80DD2093B2                 Target.attachedToTarget {"sessionId":"E9DC518DBE02B11BF57777F7C5A63E4A","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E","title":"","type":"page","url":"about:blank"},"waitingForDebugger":false}
00:53:07.66364800 21D161B4B26DD1D7AE000A80DD2093B2                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:07.66368000 21D161B4B26DD1D7AE000A80DD2093B2        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.66407100 AF12083A575E9D9A8DE7681E4B6A7AE0        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.67141100 E9DC518DBE02B11BF57777F7C5A63E4A                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:07.67149600 E9DC518DBE02B11BF57777F7C5A63E4A                          Fetch.disable* {} => {}
00:53:07.67152900 375C7099668BFC59D80D70C9A1626AE5               Target.detachedFromTarget {"sessionId":"AF12083A575E9D9A8DE7681E4B6A7AE0","targetId":"7C2A363B8EA8BD5060658242BE3D0A1A"}
00:53:07.67157700             browser                             Target.targetInfoChanged {"targetInfo":{"attached":false,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:07.67161500 375C7099668BFC59D80D70C9A1626AE5                Target.detachFromTarget* {"sessionId":"AF12083A575E9D9A8DE7681E4B6A7AE0"} => {}
00:53:07.69671300 E9DC518DBE02B11BF57777F7C5A63E4A                   Network.policyUpdated {}
00:53:07.69785700             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E","title":"about:blank","type":"page","url":"about:blank"}}
00:53:07.69994500 E9DC518DBE02B11BF57777F7C5A63E4A                Page.frameStoppedLoading {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E"}
00:53:07.69999500 E9DC518DBE02B11BF57777F7C5A63E4A        Runtime.runIfWaitingForDebugger* {} => {}
00:53:07.70002300 E9DC518DBE02B11BF57777F7C5A63E4A                         Network.enable* {} => {}
00:53:07.70016300 E9DC518DBE02B11BF57777F7C5A63E4A               Network.setCacheDisabled* {"cacheDisabled":false} => {}
00:53:07.70019000 E9DC518DBE02B11BF57777F7C5A63E4A                            Page.enable* {} => {}
00:53:07.70022200 E9DC518DBE02B11BF57777F7C5A63E4A                      Page.getFrameTree* {} => {"frameTree":{"frame":{"adFrameStatus":{"adFrameType":"none"},"crossOriginIsolatedContextType":"NotIsolated","domainAndRegistry":"","gatedAPIFeatures":[],"id":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"3B294F28CA12DF71A3CA3EBFAD7CB094","mimeType":"text/html","secureContextType":"InsecureScheme","securityOrigin":"://","securityOriginDetails":{"isLocalhost":false},"url":"about:blank"}}}
00:53:07.70029400 E9DC518DBE02B11BF57777F7C5A63E4A                     Page.lifecycleEvent {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"3B294F28CA12DF71A3CA3EBFAD7CB094","name":"commit","timestamp":531788.502622}
00:53:07.70034200 E9DC518DBE02B11BF57777F7C5A63E4A                     Page.lifecycleEvent {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"3B294F28CA12DF71A3CA3EBFAD7CB094","name":"DOMContentLoaded","timestamp":531788.502762}
00:53:07.70040500 E9DC518DBE02B11BF57777F7C5A63E4A                     Page.lifecycleEvent {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"3B294F28CA12DF71A3CA3EBFAD7CB094","name":"load","timestamp":531788.503492}
00:53:07.70042700 E9DC518DBE02B11BF57777F7C5A63E4A                     Page.lifecycleEvent {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"3B294F28CA12DF71A3CA3EBFAD7CB094","name":"networkAlmostIdle","timestamp":531788.503356}
00:53:07.70045300 E9DC518DBE02B11BF57777F7C5A63E4A                     Page.lifecycleEvent {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"3B294F28CA12DF71A3CA3EBFAD7CB094","name":"networkIdle","timestamp":531788.503356}
00:53:07.70047200 E9DC518DBE02B11BF57777F7C5A63E4A         Page.setLifecycleEventsEnabled* {"enabled":true} => {}
00:53:07.70049500 E9DC518DBE02B11BF57777F7C5A63E4A         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","isDefault":true,"type":"default"},"id":1,"name":"","origin":"://","uniqueId":"4266096281829490276.-4602349229080146416"}}
00:53:07.70051600 E9DC518DBE02B11BF57777F7C5A63E4A                         Runtime.enable* {} => {}
00:53:07.70053300 E9DC518DBE02B11BF57777F7C5A63E4A                     Performance.enable* {} => {}
00:53:07.70055000 E9DC518DBE02B11BF57777F7C5A63E4A                             Log.enable* {} => {}
00:53:07.70346900 E9DC518DBE02B11BF57777F7C5A63E4A  Page.addScriptToEvaluateOnNewDocument* {"source":"//# sourceURL=pptr:internal","worldName":"__puppeteer_utility_world__24.6.1"} => {"identifier":"1"}
00:53:07.71280400 E9DC518DBE02B11BF57777F7C5A63E4A         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","isDefault":false,"type":"isolated"},"id":2,"name":"__puppeteer_utility_world__24.6.1","origin":"","uniqueId":"8518418990080310745.-961781132864287243"}}
00:53:07.71289100 E9DC518DBE02B11BF57777F7C5A63E4A               Page.createIsolatedWorld* {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","grantUniveralAccess":true,"worldName":"__puppeteer_utility_world__24.6.1"} => {"executionContextId":2}
00:53:07.71388800 E9DC518DBE02B11BF57777F7C5A63E4A                       Page.frameResized {}
00:53:07.71402000 E9DC518DBE02B11BF57777F7C5A63E4A     Emulation.setDeviceMetricsOverride* {"deviceScaleFactor":1,"height":600,"mobile":false,"screenOrientation":{"angle":0,"type":"portraitPrimary"},"width":800} => {}
00:53:07.71405700 E9DC518DBE02B11BF57777F7C5A63E4A     Emulation.setTouchEmulationEnabled* {"enabled":false} => {}
00:53:07.71507200             browser                              Target.attachedToTarget {"sessionId":"84D1D20D6C6C6F9293494A99931815E3","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E","title":"about:blank","type":"page","url":"about:blank"},"waitingForDebugger":false}
00:53:07.71510600             browser                                Target.attachToTarget {"flatten":true,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E"} => {"sessionId":"84D1D20D6C6C6F9293494A99931815E3"}
00:53:07.71568600 E9DC518DBE02B11BF57777F7C5A63E4A                        Network.disable* {} => {}
00:53:07.71605000 E9DC518DBE02B11BF57777F7C5A63E4A                    Performance.disable* {} => {}
00:53:07.71642300 E9DC518DBE02B11BF57777F7C5A63E4A                        Runtime.disable* {} => {}
00:53:07.71679000 E9DC518DBE02B11BF57777F7C5A63E4A                            Log.disable* {} => {}
00:53:07.71715700 E9DC518DBE02B11BF57777F7C5A63E4A                           Page.disable* {} => {}
00:53:07.71747500 84D1D20D6C6C6F9293494A99931815E3                        Network.disable* {} => {}
00:53:07.71778300 84D1D20D6C6C6F9293494A99931815E3                            Page.enable* {} => {}
00:53:07.72049100 84D1D20D6C6C6F9293494A99931815E3  Page.addScriptToEvaluateOnNewDocument* {"source":"// Input Focus Listener Script\n// This script can be injected into pages to monitor input element focus events\n// It works in both main documents and iframes, with dynamic content detection\n\n// This runs in an isolated world, separate from the page's main world\nconsole.log(\"🚀 [ISOLATED WORLD] Script loaded and initialized\");\n\n// Function to add focus listeners to all input elements\nfunction addFocusListeners(doc = document, context = \"main\") {\n  console.log(\n    `🔍 [ISOLATED WORLD] Adding focus listeners - Context: ${context}, Document: ${\n      doc.URL || \"N/A\"\n    }`\n  );\n\n  // Use getElementsByTagName for better performance\n  const inputElements = doc.getElementsByTagName(\"input\");\n  const textareaElements = doc.getElementsByTagName(\"textarea\");\n  const selectElements = doc.getElementsByTagName(\"select\");\n\n  console.log(\n    `📊 [ISOLATED WORLD] Found elements - Inputs: ${inputElements.length}, Textareas: ${textareaElements.length}, Selects: ${selectElements.length}`\n  );\n\n  // Convert HTMLCollections to arrays and combine\n  const allInputs = [\n    ...Array.from(inputElements),\n    ...Array.from(textareaElements),\n    ...Array.from(selectElements),\n  ];\n\n  let newListenersAdded = 0;\n  let existingListeners = 0;\n\n  allInputs.forEach((input, index) =\u003e {\n    // Only add listener if not already added\n    if (!input.hasAttribute(\"data-focus-listener-added\")) {\n      input.setAttribute(\"data-focus-listener-added\", \"true\");\n      newListenersAdded++;\n\n      console.log(\n        `➕ [ISOLATED WORLD] Adding listener to element ${index + 1}/${\n          allInputs.length\n        }:`,\n        {\n          tagName: input.tagName,\n          type: input.type || \"N/A\",\n          id: input.id || \"N/A\",\n          className: input.className || \"N/A\",\n          context: context,\n        }\n      );\n\n      input.addEventListener(\"focus\", (event) =\u003e {\n        console.log(`🎯 [ISOLATED WORLD] ===== INPUT FOCUSED =====`);\n        console.log(`📍 [ISOLATED WORLD] Element Details:`, {\n          tagName: event.target.tagName,\n          type: event.target.type || \"N/A\",\n          id: event.target.id || \"N/A\",\n          className: event.target.className || \"N/A\",\n          placeholder: event.target.placeholder || \"N/A\",\n          name: event.target.name || \"N/A\",\n          value: event.target.value || \"N/A\",\n          tabIndex: event.target.tabIndex,\n          required: event.target.required,\n          disabled: event.target.disabled,\n          readonly: event.target.readOnly,\n        });\n\n        console.log(`⏰ [ISOLATED WORLD] Event Details:`, {\n          timestamp: new Date().toISOString(),\n          eventType: event.type,\n          bubbles: event.bubbles,\n          cancelable: event.cancelable,\n          isTrusted: event.isTrusted,\n        });\n\n        console.log(`🎯 [ISOLATED WORLD] ===== END FOCUS EVENT =====`);\n      });\n    } else {\n      existingListeners++;\n    }\n  });\n\n  console.log(\n    `✅ [ISOLATED WORLD] Listener summary - New: ${newListenersAdded}, Existing: ${existingListeners}, Total elements: ${allInputs.length}`\n  );\n}\n\n// Detect if we're in an iframe or main document\nconst isIframe = window !== window.top;\nconst contextType = isIframe ? \"iframe\" : \"main-document\";\n\nconsole.log(`🏁 [ISOLATED WORLD] Initial setup - Context: ${contextType}, Document ready state: ${document.readyState}, URL: ${document.URL}`);\n\n// Add listeners to current document (works for both main document and iframes)\nif (document.readyState === \"loading\") {\n  console.log(`⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation`);\n  document.addEventListener(\"DOMContentLoaded\", () =\u003e {\n    console.log(\n      `✅ [ISOLATED WORLD] DOMContentLoaded fired for ${contextType}, adding listeners`\n    );\n    addFocusListeners(document, `${contextType}-loaded`);\n  });\n} else {\n  console.log(`✅ [ISOLATED WORLD] Document already loaded for ${contextType}, adding listeners immediately`);\n  addFocusListeners(document, `${contextType}-immediate`);\n}\n\nlet mutationCount = 0;\nconst observer = new MutationObserver((mutations) =\u003e {\n  mutationCount++;\n  console.log(\n    `🔄 [ISOLATED WORLD] Mutation batch #${mutationCount} detected with ${mutations.length} mutations`\n  );\n\n  let hasNewInputs = false;\n  let hasNewIframes = false;\n  let inputsFound = 0;\n  let iframesFound = 0;\n\n  mutations.forEach((mutation, mutIndex) =\u003e {\n    if (mutation.type === \"childList\") {\n      console.log(\n        `🔄 [ISOLATED WORLD] Processing mutation ${mutIndex + 1}/${\n          mutations.length\n        } - Added: ${mutation.addedNodes.length}, Removed: ${\n          mutation.removedNodes.length\n        }`\n      );\n\n      mutation.addedNodes.forEach((node, nodeIndex) =\u003e {\n        if (node.nodeType === Node.ELEMENT_NODE) {\n          console.log(\n            `➕ [ISOLATED WORLD] Analyzing added node ${nodeIndex + 1}: ${\n              node.tagName || \"Unknown\"\n            }`\n          );\n\n          // Check if new node is an input or contains inputs\n          if (node.matches \u0026\u0026 node.matches(\"input, textarea, select\")) {\n            console.log(\n              `🎯 [ISOLATED WORLD] Direct input element added: ${node.tagName}`\n            );\n            hasNewInputs = true;\n            inputsFound++;\n          } else if (node.querySelectorAll) {\n            const newInputs = node.querySelectorAll(\n              \"input, textarea, select\"\n            );\n            if (newInputs.length \u003e 0) {\n              console.log(\n                `🎯 [ISOLATED WORLD] Container with ${newInputs.length} input elements added`\n              );\n              hasNewInputs = true;\n              inputsFound += newInputs.length;\n            }\n          }\n\n          // Check for new iframes\n          if (node.matches \u0026\u0026 node.matches(\"iframe\")) {\n            console.log(`🖼️ [ISOLATED WORLD] Direct iframe element added`);\n            hasNewIframes = true;\n            iframesFound++;\n          } else if (node.querySelectorAll) {\n            const newIframes = node.querySelectorAll(\"iframe\");\n            if (newIframes.length \u003e 0) {\n              console.log(\n                `🖼️ [ISOLATED WORLD] Container with ${newIframes.length} iframe elements added`\n              );\n              hasNewIframes = true;\n              iframesFound += newIframes.length;\n            }\n          }\n        }\n      });\n    }\n  });\n\n  // Process findings\n  if (hasNewInputs || hasNewIframes) {\n    console.log(\n      `🔄 [ISOLATED WORLD] Mutation summary - Inputs: ${inputsFound}, Iframes: ${iframesFound}`\n    );\n\n    if (hasNewInputs) {\n      console.log(\n        `🔄 [ISOLATED WORLD] Re-scanning document for new input elements...`\n      );\n      addFocusListeners(document, `${contextType}-mutation`);\n    }\n    if (hasNewIframes) {\n      console.log(\n        `🔄 [ISOLATED WORLD] Re-scanning document for new iframe elements...`\n      );\n    }\n  } else {\n    console.log(\n      `🔄 [ISOLATED WORLD] No relevant elements found in this mutation batch`\n    );\n  }\n});\n\n// Start observing\nconsole.log(`👀 [ISOLATED WORLD] Starting mutation observer for ${contextType}...`);\nif (document.body) {\n  console.log(\n    `✅ [ISOLATED WORLD] Document body available for ${contextType}, starting observation immediately`\n  );\n  observer.observe(document.body, {\n    childList: true,\n    subtree: true,\n  });\n} else {\n  console.log(\n    `⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation`,\n    document.URL\n  );\n  document.addEventListener(\"DOMContentLoaded\", () =\u003e {\n    console.log(\n      `✅ [ISOLATED WORLD] DOMContentLoaded fired for ${contextType}, starting mutation observation`\n    );\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true,\n    });\n  });\n}\nconsole.log(`🎉 [ISOLATED WORLD] All initialization complete!`);\n","worldName":"testing-world"} => {"identifier":"1"}
00:53:07.78105800 84D1D20D6C6C6F9293494A99931815E3             Page.frameStartedNavigating {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"39EEBE8052CA1BA9DC82CD11B7C4174D","navigationType":"differentDocument","url":"https://2captcha.com/demo/mtcaptcha"}
00:53:07.78113700 84D1D20D6C6C6F9293494A99931815E3                Page.frameStartedLoading {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E"}
00:53:08.47402900 84D1D20D6C6C6F9293494A99931815E3                          Page.navigate* {"url":"https://2captcha.com/demo/mtcaptcha"} => {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"39EEBE8052CA1BA9DC82CD11B7C4174D"}
00:53:08.47537800 84D1D20D6C6C6F9293494A99931815E3                     Page.frameNavigated {"frame":{"adFrameStatus":{"adFrameType":"none"},"crossOriginIsolatedContextType":"NotIsolated","domainAndRegistry":"2captcha.com","gatedAPIFeatures":[],"id":"3CCDFDEDD57A016CD1E4420E5B64487E","loaderId":"39EEBE8052CA1BA9DC82CD11B7C4174D","mimeType":"text/html","secureContextType":"Secure","securityOrigin":"https://2captcha.com","securityOriginDetails":{"isLocalhost":false},"url":"https://2captcha.com/demo/mtcaptcha"},"type":"Navigation"}
00:53:08.47838900             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E","title":"2captcha.com/demo/mtcaptcha","type":"page","url":"https://2captcha.com/demo/mtcaptcha"}}
00:53:08.81161400 84D1D20D6C6C6F9293494A99931815E3                      Page.frameAttached {"frameId":"19150D35A744B143BA4587042AFEA0BB","parentFrameId":"3CCDFDEDD57A016CD1E4420E5B64487E","stack":{"callFrames":[{"columnNumber":441,"functionName":"zc","lineNumber":132,"scriptId":"8","url":"https://www.googletagmanager.com/gtm.js?id=GTM-54JRQLV"},{"columnNumber":2,"functionName":"","lineNumber":523,"scriptId":"8","url":"https://www.googletagmanager.com/gtm.js?id=GTM-54JRQLV"},{"columnNumber":163,"functionName":"","lineNumber":522,"scriptId":"8","url":"https://www.googletagmanager.com/gtm.js?id=GTM-54JRQLV"}]}}
00:53:08.81222200 84D1D20D6C6C6F9293494A99931815E3           Page.frameRequestedNavigation {"disposition":"currentTab","frameId":"19150D35A744B143BA4587042AFEA0BB","reason":"initialFrameNavigation","url":"about:blank"}
00:53:08.81232000 84D1D20D6C6C6F9293494A99931815E3                     Page.frameNavigated {"frame":{"adFrameStatus":{"adFrameType":"none","explanations":[]},"crossOriginIsolatedContextType":"NotIsolated","domainAndRegistry":"","gatedAPIFeatures":[],"id":"19150D35A744B143BA4587042AFEA0BB","loaderId":"AF2D2B01C487BBBD716842CD03F284A5","mimeType":"text/html","name":"","parentId":"3CCDFDEDD57A016CD1E4420E5B64487E","secureContextType":"Secure","securityOrigin":"://","securityOriginDetails":{"isLocalhost":false},"url":"about:blank"},"type":"Navigation"}
00:53:08.81235300 84D1D20D6C6C6F9293494A99931815E3                Page.frameStartedLoading {"frameId":"19150D35A744B143BA4587042AFEA0BB"}
00:53:08.88023500 84D1D20D6C6C6F9293494A99931815E3                Page.frameStoppedLoading {"frameId":"19150D35A744B143BA4587042AFEA0BB"}
00:53:08.88071900 84D1D20D6C6C6F9293494A99931815E3                      Page.frameAttached {"frameId":"520832D294BB8C218AB3FC39FAB43502","parentFrameId":"19150D35A744B143BA4587042AFEA0BB","stack":{"callFrames":[{"columnNumber":322,"functionName":"q","lineNumber":523,"scriptId":"8","url":"https://www.googletagmanager.com/gtm.js?id=GTM-54JRQLV"},{"columnNumber":42,"functionName":"","lineNumber":524,"scriptId":"8","url":"https://www.googletagmanager.com/gtm.js?id=GTM-54JRQLV"},{"columnNumber":163,"functionName":"","lineNumber":522,"scriptId":"8","url":"https://www.googletagmanager.com/gtm.js?id=GTM-54JRQLV"}]}}
00:53:08.88081000 84D1D20D6C6C6F9293494A99931815E3           Page.frameRequestedNavigation {"disposition":"currentTab","frameId":"520832D294BB8C218AB3FC39FAB43502","reason":"initialFrameNavigation","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}
00:53:08.94074500 84D1D20D6C6C6F9293494A99931815E3             Page.frameStartedNavigating {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"5C8D49A831FAB7C461B14836758EE5AD","navigationType":"differentDocument","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}
00:53:08.94082600 84D1D20D6C6C6F9293494A99931815E3                Page.frameStartedLoading {"frameId":"520832D294BB8C218AB3FC39FAB43502"}
00:53:08.94234000             browser                                 Target.targetCreated {"targetInfo":{"attached":false,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"520832D294BB8C218AB3FC39FAB43502","title":"","type":"iframe","url":""}}
00:53:08.94240400             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"520832D294BB8C218AB3FC39FAB43502","title":"","type":"iframe","url":""}}
00:53:08.94244200 E9DC518DBE02B11BF57777F7C5A63E4A                 Target.attachedToTarget {"sessionId":"95AB0074AF283909F77F4EB42F86174A","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"520832D294BB8C218AB3FC39FAB43502","title":"","type":"iframe","url":""},"waitingForDebugger":true}
00:53:08.94407800 95AB0074AF283909F77F4EB42F86174A                          Fetch.disable* {} => {}
00:53:08.94428900 95AB0074AF283909F77F4EB42F86174A                         Network.enable* {} => {}
00:53:08.94432800 95AB0074AF283909F77F4EB42F86174A               Network.setCacheDisabled* {"cacheDisabled":false} => {}
00:53:08.94436300 95AB0074AF283909F77F4EB42F86174A               Network.setCacheDisabled* {"cacheDisabled":false} => {}
00:53:08.94441800 95AB0074AF283909F77F4EB42F86174A                            Page.enable* {} => {}
00:53:08.94445700 95AB0074AF283909F77F4EB42F86174A                      Page.getFrameTree* {} => {"frameTree":{"frame":{"adFrameStatus":{"adFrameType":"none"},"crossOriginIsolatedContextType":"NotIsolatedFeatureDisabled","domainAndRegistry":"","gatedAPIFeatures":[],"id":"520832D294BB8C218AB3FC39FAB43502","loaderId":"C9A2949A5D1E78EFB2F64370EC86EFCB","mimeType":"text/html","name":"","parentId":"19150D35A744B143BA4587042AFEA0BB","secureContextType":"InsecureScheme","securityOrigin":"://","securityOriginDetails":{"isLocalhost":false},"url":":"}}}
00:53:08.94448400 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"C9A2949A5D1E78EFB2F64370EC86EFCB","name":"commit","timestamp":531789.745301}
00:53:08.94450700 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"C9A2949A5D1E78EFB2F64370EC86EFCB","name":"DOMContentLoaded","timestamp":531789.745335}
00:53:08.94452600 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"C9A2949A5D1E78EFB2F64370EC86EFCB","name":"networkAlmostIdle","timestamp":531789.745338}
00:53:08.94454900 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"C9A2949A5D1E78EFB2F64370EC86EFCB","name":"networkIdle","timestamp":531789.745338}
00:53:08.94458900 95AB0074AF283909F77F4EB42F86174A         Page.setLifecycleEventsEnabled* {"enabled":true} => {}
00:53:08.94462400 95AB0074AF283909F77F4EB42F86174A                         Runtime.enable* {} => {}
00:53:08.94465200 95AB0074AF283909F77F4EB42F86174A                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:08.94467600 95AB0074AF283909F77F4EB42F86174A        Runtime.runIfWaitingForDebugger* {} => {}
00:53:08.94610500 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"5C8D49A831FAB7C461B14836758EE5AD","name":"init","timestamp":531789.750463}
00:53:08.94624000 95AB0074AF283909F77F4EB42F86174A        Runtime.executionContextsCleared {}
00:53:08.94628200 95AB0074AF283909F77F4EB42F86174A                     Page.frameNavigated {"frame":{"adFrameStatus":{"adFrameType":"none","explanations":[]},"crossOriginIsolatedContextType":"NotIsolatedFeatureDisabled","domainAndRegistry":"googletagmanager.com","gatedAPIFeatures":[],"id":"520832D294BB8C218AB3FC39FAB43502","loaderId":"5C8D49A831FAB7C461B14836758EE5AD","mimeType":"text/html","name":"","parentId":"19150D35A744B143BA4587042AFEA0BB","secureContextType":"Secure","securityOrigin":"https://www.googletagmanager.com","securityOriginDetails":{"isLocalhost":false},"url":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"},"type":"Navigation"}
00:53:08.94670900 95AB0074AF283909F77F4EB42F86174A                   Network.policyUpdated {}
00:53:08.94685000 E9DC518DBE02B11BF57777F7C5A63E4A                Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"520832D294BB8C218AB3FC39FAB43502","title":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"iframe","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:08.94690300             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"520832D294BB8C218AB3FC39FAB43502","title":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"iframe","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw_iframe.html?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:08.94756900 95AB0074AF283909F77F4EB42F86174A         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"520832D294BB8C218AB3FC39FAB43502","isDefault":true,"type":"default"},"id":3,"name":"","origin":"https://www.googletagmanager.com","uniqueId":"-4672524084293731777.5483881462506654715"}}
00:53:08.94761300 95AB0074AF283909F77F4EB42F86174A                    Network.dataReceived {"dataLength":3545,"encodedDataLength":0,"requestId":"5C8D49A831FAB7C461B14836758EE5AD","timestamp":531789.751733}
00:53:08.94775900 95AB0074AF283909F77F4EB42F86174A  Page.addScriptToEvaluateOnNewDocument* {"source":"//# sourceURL=pptr:internal","worldName":"__puppeteer_utility_world__24.6.1"} => {"identifier":"1"}
00:53:08.94779500 95AB0074AF283909F77F4EB42F86174A                 Network.loadingFinished {"encodedDataLength":0,"requestId":"5C8D49A831FAB7C461B14836758EE5AD","timestamp":531789.746459}
00:53:08.94822600 95AB0074AF283909F77F4EB42F86174A               Page.domContentEventFired {"timestamp":531789.752577}
00:53:08.94826400 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"5C8D49A831FAB7C461B14836758EE5AD","name":"DOMContentLoaded","timestamp":531789.752577}
00:53:08.94844100 95AB0074AF283909F77F4EB42F86174A                     Page.loadEventFired {"timestamp":531789.752743}
00:53:08.94846900 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"5C8D49A831FAB7C461B14836758EE5AD","name":"load","timestamp":531789.752743}
00:53:08.94855400 95AB0074AF283909F77F4EB42F86174A                Page.frameStoppedLoading {"frameId":"520832D294BB8C218AB3FC39FAB43502"}
00:53:08.94912600 95AB0074AF283909F77F4EB42F86174A         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"520832D294BB8C218AB3FC39FAB43502","isDefault":false,"type":"isolated"},"id":4,"name":"__puppeteer_utility_world__24.6.1","origin":"","uniqueId":"7522969798131660114.-3620174713784139285"}}
00:53:08.94916100 95AB0074AF283909F77F4EB42F86174A               Page.createIsolatedWorld* {"frameId":"520832D294BB8C218AB3FC39FAB43502","grantUniveralAccess":true,"worldName":"__puppeteer_utility_world__24.6.1"} => {"executionContextId":4}
00:53:09.02220100 84D1D20D6C6C6F9293494A99931815E3                      Page.frameDetached {"frameId":"520832D294BB8C218AB3FC39FAB43502","reason":"swap"}
00:53:09.35526600 84D1D20D6C6C6F9293494A99931815E3               Page.domContentEventFired {"timestamp":531790.158861}
00:53:09.35537000 84D1D20D6C6C6F9293494A99931815E3                     Page.loadEventFired {"timestamp":531790.159207}
00:53:09.35540200 84D1D20D6C6C6F9293494A99931815E3                Page.frameStoppedLoading {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E"}
00:53:09.38316000 84D1D20D6C6C6F9293494A99931815E3                Page.frameStartedLoading {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E"}
00:53:09.44418800             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"3CCDFDEDD57A016CD1E4420E5B64487E","title":"MTCaptcha demo: Sample Form with MTCaptcha","type":"page","url":"https://2captcha.com/demo/mtcaptcha"}}
00:53:09.44428100             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:09.44433800 E9DC518DBE02B11BF57777F7C5A63E4A                 Target.attachedToTarget {"sessionId":"FC3531C195247C73C9CACD2CC57FBD58","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"},"waitingForDebugger":false}
00:53:09.44500900 84D1D20D6C6C6F9293494A99931815E3            Page.navigatedWithinDocument {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E","navigationType":"historyApi","url":"https://2captcha.com/demo/mtcaptcha"}
00:53:09.44511500 84D1D20D6C6C6F9293494A99931815E3                Page.frameStoppedLoading {"frameId":"3CCDFDEDD57A016CD1E4420E5B64487E"}
00:53:09.44628100 FC3531C195247C73C9CACD2CC57FBD58        Runtime.runIfWaitingForDebugger* {} => {}
00:53:09.44737100 E9DC518DBE02B11BF57777F7C5A63E4A               Target.detachedFromTarget {"sessionId":"FC3531C195247C73C9CACD2CC57FBD58","targetId":"7C2A363B8EA8BD5060658242BE3D0A1A"}
00:53:09.44741200             browser                             Target.targetInfoChanged {"targetInfo":{"attached":false,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"7C2A363B8EA8BD5060658242BE3D0A1A","title":"Service Worker https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com","type":"service_worker","url":"https://www.googletagmanager.com/static/service_worker/55j0/sw.js?origin=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com"}}
00:53:09.44743700 E9DC518DBE02B11BF57777F7C5A63E4A                Target.detachFromTarget* {"sessionId":"FC3531C195247C73C9CACD2CC57FBD58"} => {}
00:53:09.76964400 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"5C8D49A831FAB7C461B14836758EE5AD","name":"networkAlmostIdle","timestamp":531789.752694}
00:53:09.76974500 95AB0074AF283909F77F4EB42F86174A                     Page.lifecycleEvent {"frameId":"520832D294BB8C218AB3FC39FAB43502","loaderId":"5C8D49A831FAB7C461B14836758EE5AD","name":"networkIdle","timestamp":531789.752694}
00:53:09.77098600 84D1D20D6C6C6F9293494A99931815E3                      Page.frameAttached {"frameId":"D84546C3E72FB77F5BA9A9014540CA33","parentFrameId":"3CCDFDEDD57A016CD1E4420E5B64487E","stack":{"callFrames":[{"columnNumber":54087,"functionName":"renderDefaultAnimation","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":58237,"functionName":"generateIframe","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":41362,"functionName":"doRender","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":40037,"functionName":"tryRenderPendingQueue","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":52164,"functionName":"renderUI","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":64940,"functionName":"m.init","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":65538,"functionName":"","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":65546,"functionName":"","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"}]}}
00:53:09.77134900 84D1D20D6C6C6F9293494A99931815E3           Page.frameRequestedNavigation {"disposition":"currentTab","frameId":"D84546C3E72FB77F5BA9A9014540CA33","reason":"initialFrameNavigation","url":"about:blank"}
00:53:09.77150400 84D1D20D6C6C6F9293494A99931815E3                     Page.frameNavigated {"frame":{"adFrameStatus":{"adFrameType":"none","explanations":[]},"crossOriginIsolatedContextType":"NotIsolated","domainAndRegistry":"","gatedAPIFeatures":[],"id":"D84546C3E72FB77F5BA9A9014540CA33","loaderId":"21BAF89704AE482EB4048EC6C071310E","mimeType":"text/html","name":"mtcaptcha-animation-frame-1","parentId":"3CCDFDEDD57A016CD1E4420E5B64487E","secureContextType":"Secure","securityOrigin":"://","securityOriginDetails":{"isLocalhost":false},"url":"about:blank"},"type":"Navigation"}
00:53:09.77177100 84D1D20D6C6C6F9293494A99931815E3                Page.frameStartedLoading {"frameId":"D84546C3E72FB77F5BA9A9014540CA33"}
00:53:09.83387100 84D1D20D6C6C6F9293494A99931815E3                Page.frameStoppedLoading {"frameId":"D84546C3E72FB77F5BA9A9014540CA33"}
00:53:09.83427900 84D1D20D6C6C6F9293494A99931815E3                     Page.documentOpened {"frame":{"adFrameStatus":{"adFrameType":"none","explanations":[]},"crossOriginIsolatedContextType":"NotIsolated","domainAndRegistry":"2captcha.com","gatedAPIFeatures":[],"id":"D84546C3E72FB77F5BA9A9014540CA33","loaderId":"21BAF89704AE482EB4048EC6C071310E","mimeType":"text/html","name":"mtcaptcha-animation-frame-1","parentId":"3CCDFDEDD57A016CD1E4420E5B64487E","secureContextType":"Secure","securityOrigin":"https://2captcha.com","securityOriginDetails":{"isLocalhost":false},"url":"https://2captcha.com/demo/mtcaptcha"}}
00:53:09.83435300 84D1D20D6C6C6F9293494A99931815E3                      Page.frameAttached {"frameId":"183905013E89E7189B1F2770BA24C1A4","parentFrameId":"3CCDFDEDD57A016CD1E4420E5B64487E","stack":{"callFrames":[{"columnNumber":59653,"functionName":"generateIframe","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":41362,"functionName":"doRender","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":40037,"functionName":"tryRenderPendingQueue","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":52164,"functionName":"renderUI","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":64940,"functionName":"m.init","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":65538,"functionName":"","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"},{"columnNumber":65546,"functionName":"","lineNumber":0,"scriptId":"280","url":"https://service.mtcaptcha.com/mtcv1/client/mtcaptcha.min.js"}]}}
00:53:09.83440900 84D1D20D6C6C6F9293494A99931815E3           Page.frameRequestedNavigation {"disposition":"currentTab","frameId":"183905013E89E7189B1F2770BA24C1A4","reason":"initialFrameNavigation","url":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false"}
00:53:09.89289700 84D1D20D6C6C6F9293494A99931815E3             Page.frameStartedNavigating {"frameId":"183905013E89E7189B1F2770BA24C1A4","loaderId":"D2B434377989131A45FFF32E23C93AEE","navigationType":"differentDocument","url":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false"}
00:53:09.89308800 84D1D20D6C6C6F9293494A99931815E3                Page.frameStartedLoading {"frameId":"183905013E89E7189B1F2770BA24C1A4"}
00:53:09.89565200             browser                                 Target.targetCreated {"targetInfo":{"attached":false,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"183905013E89E7189B1F2770BA24C1A4","title":"","type":"iframe","url":""}}
00:53:09.89575500             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"183905013E89E7189B1F2770BA24C1A4","title":"","type":"iframe","url":""}}
00:53:09.89579900 E9DC518DBE02B11BF57777F7C5A63E4A                 Target.attachedToTarget {"sessionId":"7445783CC42610213AF67AED7EB251CD","targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"183905013E89E7189B1F2770BA24C1A4","title":"","type":"iframe","url":""},"waitingForDebugger":true}
00:53:09.89648700 7445783CC42610213AF67AED7EB251CD                          Fetch.disable* {} => {}
00:53:09.89661600 7445783CC42610213AF67AED7EB251CD                         Network.enable* {} => {}
00:53:09.89664800 7445783CC42610213AF67AED7EB251CD               Network.setCacheDisabled* {"cacheDisabled":false} => {}
00:53:09.89667000 7445783CC42610213AF67AED7EB251CD               Network.setCacheDisabled* {"cacheDisabled":false} => {}
00:53:09.89683500 7445783CC42610213AF67AED7EB251CD                            Page.enable* {} => {}
00:53:09.89687100 7445783CC42610213AF67AED7EB251CD                      Page.getFrameTree* {} => {"frameTree":{"frame":{"adFrameStatus":{"adFrameType":"none"},"crossOriginIsolatedContextType":"NotIsolatedFeatureDisabled","domainAndRegistry":"","gatedAPIFeatures":[],"id":"183905013E89E7189B1F2770BA24C1A4","loaderId":"C5B7CEB85FD565D6CA717F25A07A91E9","mimeType":"text/html","name":"","parentId":"3CCDFDEDD57A016CD1E4420E5B64487E","secureContextType":"InsecureScheme","securityOrigin":"://","securityOriginDetails":{"isLocalhost":false},"url":":"}}}
00:53:09.89695900 7445783CC42610213AF67AED7EB251CD                     Page.lifecycleEvent {"frameId":"183905013E89E7189B1F2770BA24C1A4","loaderId":"C5B7CEB85FD565D6CA717F25A07A91E9","name":"commit","timestamp":531790.697389}
00:53:09.89699200 7445783CC42610213AF67AED7EB251CD                     Page.lifecycleEvent {"frameId":"183905013E89E7189B1F2770BA24C1A4","loaderId":"C5B7CEB85FD565D6CA717F25A07A91E9","name":"DOMContentLoaded","timestamp":531790.697413}
00:53:09.89701700 7445783CC42610213AF67AED7EB251CD                     Page.lifecycleEvent {"frameId":"183905013E89E7189B1F2770BA24C1A4","loaderId":"C5B7CEB85FD565D6CA717F25A07A91E9","name":"networkAlmostIdle","timestamp":531790.697416}
00:53:09.89703700 7445783CC42610213AF67AED7EB251CD                     Page.lifecycleEvent {"frameId":"183905013E89E7189B1F2770BA24C1A4","loaderId":"C5B7CEB85FD565D6CA717F25A07A91E9","name":"networkIdle","timestamp":531790.697416}
00:53:09.89705900 7445783CC42610213AF67AED7EB251CD         Page.setLifecycleEventsEnabled* {"enabled":true} => {}
00:53:09.89707500 7445783CC42610213AF67AED7EB251CD                         Runtime.enable* {} => {}
00:53:09.89709300 7445783CC42610213AF67AED7EB251CD                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:53:09.89711000 7445783CC42610213AF67AED7EB251CD        Runtime.runIfWaitingForDebugger* {} => {}
00:53:09.89818900 7445783CC42610213AF67AED7EB251CD                     Page.lifecycleEvent {"frameId":"183905013E89E7189B1F2770BA24C1A4","loaderId":"D2B434377989131A45FFF32E23C93AEE","name":"init","timestamp":531790.702576}
00:53:09.89832000 7445783CC42610213AF67AED7EB251CD        Runtime.executionContextsCleared {}
00:53:09.89838200 7445783CC42610213AF67AED7EB251CD                     Page.frameNavigated {"frame":{"adFrameStatus":{"adFrameType":"none","explanations":[]},"crossOriginIsolatedContextType":"NotIsolatedFeatureDisabled","domainAndRegistry":"mtcaptcha.com","gatedAPIFeatures":[],"id":"183905013E89E7189B1F2770BA24C1A4","loaderId":"D2B434377989131A45FFF32E23C93AEE","mimeType":"text/html","name":"","parentId":"3CCDFDEDD57A016CD1E4420E5B64487E","secureContextType":"Secure","securityOrigin":"https://service.mtcaptcha.com","securityOriginDetails":{"isLocalhost":false},"url":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false"},"type":"Navigation"}
00:53:09.89884400 7445783CC42610213AF67AED7EB251CD                   Network.policyUpdated {}
00:53:09.89903900 E9DC518DBE02B11BF57777F7C5A63E4A                Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"183905013E89E7189B1F2770BA24C1A4","title":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false","type":"iframe","url":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false"}}
00:53:09.89912000             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"6889A592C9B460430234B1BE10E9A509","canAccessOpener":false,"targetId":"183905013E89E7189B1F2770BA24C1A4","title":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false","type":"iframe","url":"https://service.mtcaptcha.com/mtcv1/client/iframe.html?v=2024-11-***********\u0026sitekey=MTPublic-KzqLY1cKH\u0026iframeId=mtcaptcha-iframe-1\u0026widgetSize=standard\u0026custom=false\u0026widgetInstance=mtcaptcha\u0026challengeType=standard\u0026theme=basic\u0026lang=en\u0026action=\u0026autoFadeOuterText=false\u0026host=https%!A(MISSING)%!F(MISSING)%!F(MISSING)2captcha.com\u0026hostname=2captcha.com\u0026serviceDomain=service.mtcaptcha.com\u0026textLength=0\u0026lowFrictionInvisible=\u0026enableMouseFlow=false"}}
00:53:09.89934300 84D1D20D6C6C6F9293494A99931815E3                      Page.frameDetached {"frameId":"183905013E89E7189B1F2770BA24C1A4","reason":"swap"}
00:53:09.90142900 7445783CC42610213AF67AED7EB251CD         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"183905013E89E7189B1F2770BA24C1A4","isDefault":true,"type":"default"},"id":3,"name":"","origin":"https://service.mtcaptcha.com","uniqueId":"-1380079241390245419.1323542407407999208"}}
00:53:09.90146500 7445783CC42610213AF67AED7EB251CD                    Network.dataReceived {"dataLength":418561,"encodedDataLength":0,"requestId":"D2B434377989131A45FFF32E23C93AEE","timestamp":531790.703833}
00:53:09.90148600 7445783CC42610213AF67AED7EB251CD  Page.addScriptToEvaluateOnNewDocument* {"source":"//# sourceURL=pptr:internal","worldName":"__puppeteer_utility_world__24.6.1"} => {"identifier":"1"}
