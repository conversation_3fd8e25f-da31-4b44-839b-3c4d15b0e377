2025/06/03 00:55:12 Proxy is listening for DevTools connections on: localhost:9223
00:55:16.61101700 ---------- connection from 127.0.0.1:58978 to /devtools/browser/f201b25c-dce0-4785-9c83-33331190cd67 ----------
00:55:16.61109800 checking protocol versions on: ws://localhost:9222/devtools/browser/f201b25c-dce0-4785-9c83-33331190cd67
00:55:16.61101400 Legend: protocol informations, received events, sent request frames, requests params, received responses, error response.
00:55:16.61159500 protocol version: 1.3  
00:55:16.61162900 versions: Chrome(Chrome/135.0.7049.84), V8(***********), Webkit(537.36 (@6c019e56001911b3fd467e03bf68c435924d62f4))
00:55:16.61164500 browser user agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
00:55:16.61167400 connecting to ws://localhost:9222/devtools/browser/f201b25c-dce0-4785-9c83-33331190cd67... 
00:55:16.61277600 upgrading connection on 127.0.0.1:58978...
00:55:16.61376900             browser                            Target.getBrowserContexts {} => {"browserContextIds":[]}
00:55:16.61422000             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"canAccessOpener":false,"targetId":"1a779c93-f096-4901-a220-921ddfe54ec5","title":"","type":"browser","url":""}}
00:55:16.61425100             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"219FA83C7E80882B69B65B617A8B8EB4","title":"about:blank","type":"tab","url":"about:blank"}}
00:55:16.61428900             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"BBF4DFB44C23F1CFA2B0946510D3113A","title":"about:blank","type":"page","url":"about:blank"}}
00:55:16.61431400             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"canAccessOpener":false,"targetId":"f50b936f-6cb4-4d8c-a818-6b206046b30e","title":"","type":"browser","url":""}}
00:55:16.61433300             browser                            Target.setDiscoverTargets {"discover":true,"filter":[{}]} => {}
00:55:16.61497300             browser                              Target.attachedToTarget {"sessionId":"0BD4F14EADF31F1A034B79E84D496E61","targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"219FA83C7E80882B69B65B617A8B8EB4","title":"about:blank","type":"tab","url":"about:blank"},"waitingForDebugger":false}
00:55:16.61500100             browser                                 Target.setAutoAttach {"autoAttach":true,"filter":[{"exclude":true,"type":"page"},{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:55:16.61536000 0BD4F14EADF31F1A034B79E84D496E61                 Target.attachedToTarget {"sessionId":"277FF31EA22B5C30C50F9FDDDEBE42BD","targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"BBF4DFB44C23F1CFA2B0946510D3113A","title":"about:blank","type":"page","url":"about:blank"},"waitingForDebugger":false}
00:55:16.61539000 0BD4F14EADF31F1A034B79E84D496E61                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:55:16.61541900 0BD4F14EADF31F1A034B79E84D496E61        Runtime.runIfWaitingForDebugger* {} => {}
00:55:16.61584000 277FF31EA22B5C30C50F9FDDDEBE42BD                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:55:16.61595200 277FF31EA22B5C30C50F9FDDDEBE42BD        Runtime.runIfWaitingForDebugger* {} => {}
00:55:16.67753400             browser                                 Target.targetCreated {"targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"6532F50317DDCF576C6168BE81949469","title":"","type":"tab","url":""}}
00:55:16.67769000             browser                              Target.attachedToTarget {"sessionId":"682116FB8203552530846B986BE070AA","targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"6532F50317DDCF576C6168BE81949469","title":"","type":"tab","url":""},"waitingForDebugger":true}
00:55:16.67774500             browser                                 Target.targetCreated {"targetInfo":{"attached":false,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"E3B88E14D3DE8080562B427A99C04BB8","title":"","type":"page","url":"about:blank"}}
00:55:16.69282200             browser                                  Target.createTarget {"url":"about:blank"} => {"targetId":"E3B88E14D3DE8080562B427A99C04BB8"}
00:55:16.69795200             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"E3B88E14D3DE8080562B427A99C04BB8","title":"","type":"page","url":"about:blank"}}
00:55:16.69832700 682116FB8203552530846B986BE070AA                 Target.attachedToTarget {"sessionId":"CA5340011D717DFA44E20420F0102943","targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"E3B88E14D3DE8080562B427A99C04BB8","title":"","type":"page","url":"about:blank"},"waitingForDebugger":false}
00:55:16.69839500 682116FB8203552530846B986BE070AA                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:55:16.69842200 682116FB8203552530846B986BE070AA        Runtime.runIfWaitingForDebugger* {} => {}
00:55:16.69929300             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"E3B88E14D3DE8080562B427A99C04BB8","title":"about:blank","type":"page","url":"about:blank"}}
00:55:16.70123500 CA5340011D717DFA44E20420F0102943                   Target.setAutoAttach* {"autoAttach":true,"filter":[{}],"flatten":true,"waitForDebuggerOnStart":true} => {}
00:55:16.70233300 CA5340011D717DFA44E20420F0102943        Runtime.runIfWaitingForDebugger* {} => {}
00:55:16.70274400 CA5340011D717DFA44E20420F0102943                         Network.enable* {} => {}
00:55:16.70282700 CA5340011D717DFA44E20420F0102943                          Fetch.disable* {} => {}
00:55:16.70331400 CA5340011D717DFA44E20420F0102943               Network.setCacheDisabled* {"cacheDisabled":false} => {}
00:55:16.70349000 CA5340011D717DFA44E20420F0102943                            Page.enable* {} => {}
00:55:16.70354100 CA5340011D717DFA44E20420F0102943                      Page.getFrameTree* {} => {"frameTree":{"frame":{"adFrameStatus":{"adFrameType":"none"},"crossOriginIsolatedContextType":"NotIsolated","domainAndRegistry":"","gatedAPIFeatures":[],"id":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","mimeType":"text/html","secureContextType":"InsecureScheme","securityOrigin":"://","securityOriginDetails":{"isLocalhost":false},"url":"about:blank"}}}
00:55:16.70393100 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","name":"commit","timestamp":531917.484579}
00:55:16.70396800 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","name":"DOMContentLoaded","timestamp":531917.484698}
00:55:16.70399100 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","name":"load","timestamp":531917.485401}
00:55:16.70401200 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","name":"networkAlmostIdle","timestamp":531917.485275}
00:55:16.70403100 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","name":"networkIdle","timestamp":531917.485275}
00:55:16.70405100 CA5340011D717DFA44E20420F0102943         Page.setLifecycleEventsEnabled* {"enabled":true} => {}
00:55:16.70407400 CA5340011D717DFA44E20420F0102943         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"E3B88E14D3DE8080562B427A99C04BB8","isDefault":true,"type":"default"},"id":1,"name":"","origin":"://","uniqueId":"3704157202360174491.2581562690998608940"}}
00:55:16.70409700 CA5340011D717DFA44E20420F0102943                         Runtime.enable* {} => {}
00:55:16.70413900 CA5340011D717DFA44E20420F0102943                     Performance.enable* {} => {}
00:55:16.70415800 CA5340011D717DFA44E20420F0102943                             Log.enable* {} => {}
00:55:16.70825500 CA5340011D717DFA44E20420F0102943  Page.addScriptToEvaluateOnNewDocument* {"source":"//# sourceURL=pptr:internal","worldName":"__puppeteer_utility_world__24.6.1"} => {"identifier":"1"}
00:55:16.70946400 CA5340011D717DFA44E20420F0102943         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"E3B88E14D3DE8080562B427A99C04BB8","isDefault":false,"type":"isolated"},"id":2,"name":"__puppeteer_utility_world__24.6.1","origin":"","uniqueId":"3326275922092703431.-6189427678436761818"}}
00:55:16.70951700 CA5340011D717DFA44E20420F0102943               Page.createIsolatedWorld* {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","grantUniveralAccess":true,"worldName":"__puppeteer_utility_world__24.6.1"} => {"executionContextId":2}
00:55:16.71073300 CA5340011D717DFA44E20420F0102943                       Page.frameResized {}
00:55:16.71090000 CA5340011D717DFA44E20420F0102943     Emulation.setDeviceMetricsOverride* {"deviceScaleFactor":1,"height":600,"mobile":false,"screenOrientation":{"angle":0,"type":"portraitPrimary"},"width":800} => {}
00:55:16.71093500 CA5340011D717DFA44E20420F0102943     Emulation.setTouchEmulationEnabled* {"enabled":false} => {}
00:55:16.71267700 CA5340011D717DFA44E20420F0102943  Page.addScriptToEvaluateOnNewDocument* {"source":"// Input Focus Listener Script\n// This script can be injected into pages to monitor input element focus events\n// It works in both main documents and iframes, with dynamic content detection\n\n// This runs in an isolated world, separate from the page's main world\nconsole.log(\"🚀 [ISOLATED WORLD] Script loaded and initialized\");\n\n// Function to add focus listeners to all input elements\nfunction addFocusListeners(doc = document, context = \"main\") {\n  console.log(\n    `🔍 [ISOLATED WORLD] Adding focus listeners - Context: ${context}, Document: ${\n      doc.URL || \"N/A\"\n    }`\n  );\n\n  // Use getElementsByTagName for better performance\n  const inputElements = doc.getElementsByTagName(\"input\");\n  const textareaElements = doc.getElementsByTagName(\"textarea\");\n  const selectElements = doc.getElementsByTagName(\"select\");\n\n  console.log(\n    `📊 [ISOLATED WORLD] Found elements - Inputs: ${inputElements.length}, Textareas: ${textareaElements.length}, Selects: ${selectElements.length}`\n  );\n\n  // Convert HTMLCollections to arrays and combine\n  const allInputs = [\n    ...Array.from(inputElements),\n    ...Array.from(textareaElements),\n    ...Array.from(selectElements),\n  ];\n\n  let newListenersAdded = 0;\n  let existingListeners = 0;\n\n  allInputs.forEach((input, index) =\u003e {\n    // Only add listener if not already added\n    if (!input.hasAttribute(\"data-focus-listener-added\")) {\n      input.setAttribute(\"data-focus-listener-added\", \"true\");\n      newListenersAdded++;\n\n      console.log(\n        `➕ [ISOLATED WORLD] Adding listener to element ${index + 1}/${\n          allInputs.length\n        }:`,\n        {\n          tagName: input.tagName,\n          type: input.type || \"N/A\",\n          id: input.id || \"N/A\",\n          className: input.className || \"N/A\",\n          context: context,\n        }\n      );\n\n      input.addEventListener(\"focus\", (event) =\u003e {\n        console.log(`🎯 [ISOLATED WORLD] ===== INPUT FOCUSED =====`);\n        console.log(`📍 [ISOLATED WORLD] Element Details:`, {\n          tagName: event.target.tagName,\n          type: event.target.type || \"N/A\",\n          id: event.target.id || \"N/A\",\n          className: event.target.className || \"N/A\",\n          placeholder: event.target.placeholder || \"N/A\",\n          name: event.target.name || \"N/A\",\n          value: event.target.value || \"N/A\",\n          tabIndex: event.target.tabIndex,\n          required: event.target.required,\n          disabled: event.target.disabled,\n          readonly: event.target.readOnly,\n        });\n\n        console.log(`⏰ [ISOLATED WORLD] Event Details:`, {\n          timestamp: new Date().toISOString(),\n          eventType: event.type,\n          bubbles: event.bubbles,\n          cancelable: event.cancelable,\n          isTrusted: event.isTrusted,\n        });\n\n        console.log(`🎯 [ISOLATED WORLD] ===== END FOCUS EVENT =====`);\n      });\n    } else {\n      existingListeners++;\n    }\n  });\n\n  console.log(\n    `✅ [ISOLATED WORLD] Listener summary - New: ${newListenersAdded}, Existing: ${existingListeners}, Total elements: ${allInputs.length}`\n  );\n}\n\n// Detect if we're in an iframe or main document\nconst isIframe = window !== window.top;\nconst contextType = isIframe ? \"iframe\" : \"main-document\";\n\nconsole.log(`🏁 [ISOLATED WORLD] Initial setup - Context: ${contextType}, Document ready state: ${document.readyState}, URL: ${document.URL}`);\n\n// Add listeners to current document (works for both main document and iframes)\nif (document.readyState === \"loading\") {\n  console.log(`⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation`);\n  document.addEventListener(\"DOMContentLoaded\", () =\u003e {\n    console.log(\n      `✅ [ISOLATED WORLD] DOMContentLoaded fired for ${contextType}, adding listeners`\n    );\n    addFocusListeners(document, `${contextType}-loaded`);\n  });\n} else {\n  console.log(`✅ [ISOLATED WORLD] Document already loaded for ${contextType}, adding listeners immediately`);\n  addFocusListeners(document, `${contextType}-immediate`);\n}\n\nlet mutationCount = 0;\nconst observer = new MutationObserver((mutations) =\u003e {\n  mutationCount++;\n  console.log(\n    `🔄 [ISOLATED WORLD] Mutation batch #${mutationCount} detected with ${mutations.length} mutations`\n  );\n\n  let hasNewInputs = false;\n  let hasNewIframes = false;\n  let inputsFound = 0;\n  let iframesFound = 0;\n\n  mutations.forEach((mutation, mutIndex) =\u003e {\n    if (mutation.type === \"childList\") {\n      console.log(\n        `🔄 [ISOLATED WORLD] Processing mutation ${mutIndex + 1}/${\n          mutations.length\n        } - Added: ${mutation.addedNodes.length}, Removed: ${\n          mutation.removedNodes.length\n        }`\n      );\n\n      mutation.addedNodes.forEach((node, nodeIndex) =\u003e {\n        if (node.nodeType === Node.ELEMENT_NODE) {\n          console.log(\n            `➕ [ISOLATED WORLD] Analyzing added node ${nodeIndex + 1}: ${\n              node.tagName || \"Unknown\"\n            }`\n          );\n\n          // Check if new node is an input or contains inputs\n          if (node.matches \u0026\u0026 node.matches(\"input, textarea, select\")) {\n            console.log(\n              `🎯 [ISOLATED WORLD] Direct input element added: ${node.tagName}`\n            );\n            hasNewInputs = true;\n            inputsFound++;\n          } else if (node.querySelectorAll) {\n            const newInputs = node.querySelectorAll(\n              \"input, textarea, select\"\n            );\n            if (newInputs.length \u003e 0) {\n              console.log(\n                `🎯 [ISOLATED WORLD] Container with ${newInputs.length} input elements added`\n              );\n              hasNewInputs = true;\n              inputsFound += newInputs.length;\n            }\n          }\n\n          // Check for new iframes\n          if (node.matches \u0026\u0026 node.matches(\"iframe\")) {\n            console.log(`🖼️ [ISOLATED WORLD] Direct iframe element added`);\n            hasNewIframes = true;\n            iframesFound++;\n          } else if (node.querySelectorAll) {\n            const newIframes = node.querySelectorAll(\"iframe\");\n            if (newIframes.length \u003e 0) {\n              console.log(\n                `🖼️ [ISOLATED WORLD] Container with ${newIframes.length} iframe elements added`\n              );\n              hasNewIframes = true;\n              iframesFound += newIframes.length;\n            }\n          }\n        }\n      });\n    }\n  });\n\n  // Process findings\n  if (hasNewInputs || hasNewIframes) {\n    console.log(\n      `🔄 [ISOLATED WORLD] Mutation summary - Inputs: ${inputsFound}, Iframes: ${iframesFound}`\n    );\n\n    if (hasNewInputs) {\n      console.log(\n        `🔄 [ISOLATED WORLD] Re-scanning document for new input elements...`\n      );\n      addFocusListeners(document, `${contextType}-mutation`);\n    }\n    if (hasNewIframes) {\n      console.log(\n        `🔄 [ISOLATED WORLD] Re-scanning document for new iframe elements...`\n      );\n    }\n  } else {\n    console.log(\n      `🔄 [ISOLATED WORLD] No relevant elements found in this mutation batch`\n    );\n  }\n});\n\n// Start observing\nconsole.log(`👀 [ISOLATED WORLD] Starting mutation observer for ${contextType}...`);\nif (document.body) {\n  console.log(\n    `✅ [ISOLATED WORLD] Document body available for ${contextType}, starting observation immediately`\n  );\n  observer.observe(document.body, {\n    childList: true,\n    subtree: true,\n  });\n} else {\n  console.log(\n    `⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation`,\n    document.URL\n  );\n  document.addEventListener(\"DOMContentLoaded\", () =\u003e {\n    console.log(\n      `✅ [ISOLATED WORLD] DOMContentLoaded fired for ${contextType}, starting mutation observation`\n    );\n    observer.observe(document.body, {\n      childList: true,\n      subtree: true,\n    });\n  });\n}\nconsole.log(`🎉 [ISOLATED WORLD] All initialization complete!`);\n"} => {"identifier":"2"}
00:55:16.77157500 CA5340011D717DFA44E20420F0102943             Page.frameStartedNavigating {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"679A5A918ECFC3434BB2D84F13068C4C","navigationType":"differentDocument","url":"https://2captcha.com/demo/mtcaptcha"}
00:55:16.77169400 CA5340011D717DFA44E20420F0102943                Page.frameStartedLoading {"frameId":"E3B88E14D3DE8080562B427A99C04BB8"}
00:55:16.77574700 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"type":"other"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"VeryHigh","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/demo/mtcaptcha"},"requestId":"679A5A918ECFC3434BB2D84F13068C4C","timestamp":531917.579186,"type":"Document","wallTime":1748891416.775359}
00:55:17.16684600 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[],"connectTiming":{"requestTime":531917.579855},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/demo/mtcaptcha",":scheme":"https","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","priority":"u=0, i","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"679A5A918ECFC3434BB2D84F13068C4C","siteHasCookieInOtherPartition":false}
00:55:17.21222400 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","name":"networkAlmostIdle","timestamp":531917.485275}
00:55:17.21243200 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"303BCA253B7E34DC0314A9B7B1971248","name":"networkIdle","timestamp":531917.485275}
00:55:17.84057100 CA5340011D717DFA44E20420F0102943                          Page.navigate* {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","url":"https://2captcha.com/demo/mtcaptcha"} => {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"679A5A918ECFC3434BB2D84F13068C4C"}
00:55:17.84229100 CA5340011D717DFA44E20420F0102943                     Page.lifecycleEvent {"frameId":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"679A5A918ECFC3434BB2D84F13068C4C","name":"init","timestamp":531918.645717}
00:55:17.84271500 CA5340011D717DFA44E20420F0102943        Runtime.executionContextsCleared {}
00:55:17.84280300 CA5340011D717DFA44E20420F0102943                     Page.frameNavigated {"frame":{"adFrameStatus":{"adFrameType":"none"},"crossOriginIsolatedContextType":"NotIsolated","domainAndRegistry":"2captcha.com","gatedAPIFeatures":[],"id":"E3B88E14D3DE8080562B427A99C04BB8","loaderId":"679A5A918ECFC3434BB2D84F13068C4C","mimeType":"text/html","secureContextType":"Secure","securityOrigin":"https://2captcha.com","securityOriginDetails":{"isLocalhost":false},"url":"https://2captcha.com/demo/mtcaptcha"},"type":"Navigation"}
00:55:17.84427900 CA5340011D717DFA44E20420F0102943                   Network.policyUpdated {}
00:55:17.84901300             browser                             Target.targetInfoChanged {"targetInfo":{"attached":true,"browserContextId":"4D123E2411F18B69BBD240CA0B8A4206","canAccessOpener":false,"targetId":"E3B88E14D3DE8080562B427A99C04BB8","title":"2captcha.com/demo/mtcaptcha","type":"page","url":"https://2captcha.com/demo/mtcaptcha"}}
00:55:17.85004100 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":35062,"encodedDataLength":0,"requestId":"679A5A918ECFC3434BB2D84F13068C4C","timestamp":531918.647628}
00:55:17.85082600 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":539,"lineNumber":6,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"High","isLinkPreload":true,"isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/page-bg-DmKWqMn3.svg"},"requestId":"48945.2","timestamp":531918.648267,"type":"Image","wallTime":1748891417.844479}
00:55:17.85126400 CA5340011D717DFA44E20420F0102943         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"E3B88E14D3DE8080562B427A99C04BB8","isDefault":true,"type":"default"},"id":3,"name":"","origin":"https://2captcha.com","uniqueId":"-1639493031124607821.4137214921176765233"}}
00:55:17.85199900 CA5340011D717DFA44E20420F0102943         Runtime.executionContextCreated {"context":{"auxData":{"frameId":"E3B88E14D3DE8080562B427A99C04BB8","isDefault":false,"type":"isolated"},"id":4,"name":"__puppeteer_utility_world__24.6.1","origin":"://","uniqueId":"8651296038757857081.4853308922800498677"}}
00:55:17.85205500 CA5340011D717DFA44E20420F0102943                Runtime.consoleAPICalled {"args":[{"type":"string","value":"🚀 [ISOLATED WORLD] Script loaded and initialized"}],"executionContextId":3,"stackTrace":{"callFrames":[{"columnNumber":8,"functionName":"","lineNumber":5,"scriptId":"6","url":""}]},"timestamp":1748891417847.498,"type":"log"}
00:55:17.85223100 CA5340011D717DFA44E20420F0102943                Runtime.consoleAPICalled {"args":[{"type":"string","value":"🏁 [ISOLATED WORLD] Initial setup - Context: main-document, Document ready state: loading, URL: https://2captcha.com/demo/mtcaptcha"}],"executionContextId":3,"stackTrace":{"callFrames":[{"columnNumber":8,"functionName":"","lineNumber":93,"scriptId":"6","url":""}]},"timestamp":1748891417847.678,"type":"log"}
00:55:17.85226800 CA5340011D717DFA44E20420F0102943                Runtime.consoleAPICalled {"args":[{"type":"string","value":"⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation"}],"executionContextId":3,"stackTrace":{"callFrames":[{"columnNumber":10,"functionName":"","lineNumber":97,"scriptId":"6","url":""}]},"timestamp":1748891417847.73,"type":"log"}
00:55:17.85230100 CA5340011D717DFA44E20420F0102943                Runtime.consoleAPICalled {"args":[{"type":"string","value":"👀 [ISOLATED WORLD] Starting mutation observer for main-document..."}],"executionContextId":3,"stackTrace":{"callFrames":[{"columnNumber":8,"functionName":"","lineNumber":204,"scriptId":"6","url":""}]},"timestamp":1748891417847.814,"type":"log"}
00:55:17.85233200 CA5340011D717DFA44E20420F0102943                Runtime.consoleAPICalled {"args":[{"type":"string","value":"⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation"},{"type":"string","value":"https://2captcha.com/demo/mtcaptcha"}],"executionContextId":3,"stackTrace":{"callFrames":[{"columnNumber":10,"functionName":"","lineNumber":214,"scriptId":"6","url":""}]},"timestamp":1748891417847.851,"type":"log"}
00:55:17.85245200 CA5340011D717DFA44E20420F0102943                Runtime.consoleAPICalled {"args":[{"type":"string","value":"🎉 [ISOLATED WORLD] All initialization complete!"}],"executionContextId":3,"stackTrace":{"callFrames":[{"columnNumber":8,"functionName":"","lineNumber":228,"scriptId":"6","url":""}]},"timestamp":1748891417847.896,"type":"log"}
00:55:17.85595600 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[{"blockedReasons":[],"cookie":{"domain":".2captcha.com","expires":1780427417.834715,"httpOnly":false,"name":"i18next","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":9,"sourcePort":443,"sourceScheme":"Secure","value":"en"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836307,"httpOnly":false,"name":"guest_currency","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":false,"session":false,"size":17,"sourcePort":443,"sourceScheme":"Secure","value":"usd"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836399,"httpOnly":false,"name":"user_country","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":14,"sourcePort":443,"sourceScheme":"Secure","value":"np"},"exemptionReason":"None"}],"clientSecurityState":{"initiatorIPAddressSpace":"Public","initiatorIsSecureContext":true,"privateNetworkRequestPolicy":"PreflightWarn"},"connectTiming":{"requestTime":531918.648863},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/dist/web/assets/page-bg-DmKWqMn3.svg",":scheme":"https","accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cookie":"i18next=en; guest_currency=usd; user_country=np","priority":"u=1, i","referer":"https://2captcha.com/demo/mtcaptcha","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"48945.2","siteHasCookieInOtherPartition":false}
00:55:17.86965600 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":8706,"encodedDataLength":0,"requestId":"679A5A918ECFC3434BB2D84F13068C4C","timestamp":531918.67309}
00:55:17.90443400 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":4665,"encodedDataLength":0,"requestId":"48945.2","timestamp":531918.707849}
00:55:17.90696400 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":0,"encodedDataLength":1091,"requestId":"48945.2","timestamp":531918.710524}
00:55:17.90705200 CA5340011D717DFA44E20420F0102943                 Network.loadingFinished {"encodedDataLength":1796,"requestId":"48945.2","timestamp":531918.707914}
00:55:18.06401100 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":3523,"encodedDataLength":0,"requestId":"679A5A918ECFC3434BB2D84F13068C4C","timestamp":531918.867308}
00:55:18.06888800 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":4739,"encodedDataLength":0,"requestId":"679A5A918ECFC3434BB2D84F13068C4C","timestamp":531918.872277}
00:55:18.13927700 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":163715,"encodedDataLength":0,"requestId":"679A5A918ECFC3434BB2D84F13068C4C","timestamp":531918.941649}
00:55:18.14828200 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[{"blockedReasons":[],"cookie":{"domain":".2captcha.com","expires":1780427417.834715,"httpOnly":false,"name":"i18next","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":9,"sourcePort":443,"sourceScheme":"Secure","value":"en"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836307,"httpOnly":false,"name":"guest_currency","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":false,"session":false,"size":17,"sourcePort":443,"sourceScheme":"Secure","value":"usd"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836399,"httpOnly":false,"name":"user_country","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":14,"sourcePort":443,"sourceScheme":"Secure","value":"np"},"exemptionReason":"None"}],"clientSecurityState":{"initiatorIPAddressSpace":"Public","initiatorIsSecureContext":true,"privateNetworkRequestPolicy":"PreflightWarn"},"connectTiming":{"requestTime":531918.950777},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/dist/web/assets/index-MJ__H5dY.css",":scheme":"https","accept":"text/css,*/*;q=0.1","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cookie":"i18next=en; guest_currency=usd; user_country=np","origin":"https://2captcha.com","priority":"u=4","referer":"https://2captcha.com/demo/mtcaptcha","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"style","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"48945.6","siteHasCookieInOtherPartition":false}
00:55:18.14848000 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[{"blockedReasons":[],"cookie":{"domain":".2captcha.com","expires":1780427417.834715,"httpOnly":false,"name":"i18next","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":9,"sourcePort":443,"sourceScheme":"Secure","value":"en"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836307,"httpOnly":false,"name":"guest_currency","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":false,"session":false,"size":17,"sourcePort":443,"sourceScheme":"Secure","value":"usd"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836399,"httpOnly":false,"name":"user_country","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":14,"sourcePort":443,"sourceScheme":"Secure","value":"np"},"exemptionReason":"None"}],"clientSecurityState":{"initiatorIPAddressSpace":"Public","initiatorIsSecureContext":true,"privateNetworkRequestPolicy":"PreflightWarn"},"connectTiming":{"requestTime":531918.951116},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/dist/web/assets/symbol-defs-CzMgBTSR.svg",":scheme":"https","accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cookie":"i18next=en; guest_currency=usd; user_country=np","priority":"i","referer":"https://2captcha.com/demo/mtcaptcha","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"image","sec-fetch-mode":"same-origin","sec-fetch-site":"same-origin","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"48945.7","siteHasCookieInOtherPartition":false}
00:55:18.14918200 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"stack":{"callFrames":[{"columnNumber":324,"functionName":"","lineNumber":12,"scriptId":"8","url":"https://2captcha.com/demo/mtcaptcha"},{"columnNumber":345,"functionName":"","lineNumber":12,"scriptId":"8","url":"https://2captcha.com/demo/mtcaptcha"}]},"type":"script"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Referer":"https://2captcha.com/","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"Low","isSameSite":false,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://www.googletagmanager.com/gtm.js?id=GTM-54JRQLV"},"requestId":"48945.4","timestamp":531918.945939,"type":"Script","wallTime":1748891418.142302}
00:55:18.14928600 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":88,"lineNumber":277,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Origin":"https://2captcha.com","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"High","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/index-DEK6t4E-.js"},"requestId":"48945.5","timestamp":531918.949907,"type":"Script","wallTime":1748891418.146102}
00:55:18.14938300 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":141,"lineNumber":278,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Origin":"https://2captcha.com","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"VeryLow","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/index-MJ__H5dY.css"},"requestId":"48945.6","timestamp":531918.950049,"type":"Stylesheet","wallTime":1748891418.146231}
00:55:18.14946600 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":1072,"lineNumber":283,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"Low","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/symbol-defs-CzMgBTSR.svg","urlFragment":"#icon-chevron-down"},"requestId":"48945.7","timestamp":531918.95061,"type":"Other","wallTime":1748891418.146797}
00:55:18.14956300 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":344,"lineNumber":329,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"Low","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/twocaptcha-logo-C0LH6luP.svg"},"requestId":"48945.8","timestamp":531918.952186,"type":"Image","wallTime":1748891418.148367}
00:55:18.14964000 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":344,"lineNumber":329,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"Low","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/mtcaptcha-B5ZgfN9B.svg"},"requestId":"48945.9","timestamp":531918.952243,"type":"Image","wallTime":1748891418.148429}
00:55:18.14975500 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[{"blockedReasons":[],"cookie":{"domain":".2captcha.com","expires":1780427417.834715,"httpOnly":false,"name":"i18next","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":9,"sourcePort":443,"sourceScheme":"Secure","value":"en"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836307,"httpOnly":false,"name":"guest_currency","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":false,"session":false,"size":17,"sourcePort":443,"sourceScheme":"Secure","value":"usd"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836399,"httpOnly":false,"name":"user_country","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":14,"sourcePort":443,"sourceScheme":"Secure","value":"np"},"exemptionReason":"None"}],"clientSecurityState":{"initiatorIPAddressSpace":"Public","initiatorIsSecureContext":true,"privateNetworkRequestPolicy":"PreflightWarn"},"connectTiming":{"requestTime":531918.952505},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/dist/web/assets/twocaptcha-logo-C0LH6luP.svg",":scheme":"https","accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cookie":"i18next=en; guest_currency=usd; user_country=np","priority":"i","referer":"https://2captcha.com/demo/mtcaptcha","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"48945.8","siteHasCookieInOtherPartition":false}
00:55:18.14988100 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[{"blockedReasons":[],"cookie":{"domain":".2captcha.com","expires":1780427417.834715,"httpOnly":false,"name":"i18next","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":9,"sourcePort":443,"sourceScheme":"Secure","value":"en"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836307,"httpOnly":false,"name":"guest_currency","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":false,"session":false,"size":17,"sourcePort":443,"sourceScheme":"Secure","value":"usd"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836399,"httpOnly":false,"name":"user_country","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":14,"sourcePort":443,"sourceScheme":"Secure","value":"np"},"exemptionReason":"None"}],"clientSecurityState":{"initiatorIPAddressSpace":"Public","initiatorIsSecureContext":true,"privateNetworkRequestPolicy":"PreflightWarn"},"connectTiming":{"requestTime":531918.952741},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/dist/web/assets/mtcaptcha-B5ZgfN9B.svg",":scheme":"https","accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cookie":"i18next=en; guest_currency=usd; user_country=np","priority":"i","referer":"https://2captcha.com/demo/mtcaptcha","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"48945.9","siteHasCookieInOtherPartition":false}
00:55:18.15505500 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[{"blockedReasons":[],"cookie":{"domain":".2captcha.com","expires":1780427417.834715,"httpOnly":false,"name":"i18next","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":9,"sourcePort":443,"sourceScheme":"Secure","value":"en"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836307,"httpOnly":false,"name":"guest_currency","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":false,"session":false,"size":17,"sourcePort":443,"sourceScheme":"Secure","value":"usd"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836399,"httpOnly":false,"name":"user_country","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":14,"sourcePort":443,"sourceScheme":"Secure","value":"np"},"exemptionReason":"None"}],"clientSecurityState":{"initiatorIPAddressSpace":"Public","initiatorIsSecureContext":true,"privateNetworkRequestPolicy":"PreflightWarn"},"connectTiming":{"requestTime":531918.958139},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/dist/web/assets/page-bg-D_vxkDfD.svg",":scheme":"https","accept":"image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cookie":"i18next=en; guest_currency=usd; user_country=np","priority":"i","referer":"https://2captcha.com/demo/mtcaptcha","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"image","sec-fetch-mode":"no-cors","sec-fetch-site":"same-origin","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"48945.236","siteHasCookieInOtherPartition":false}
00:55:18.19904700 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":4,"lineNumber":330,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"Low","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/page-bg-D_vxkDfD.svg"},"requestId":"48945.236","timestamp":531918.957884,"type":"Image","wallTime":1748891418.15407}
00:55:18.19932800 CA5340011D717DFA44E20420F0102943               Network.requestWillBeSent {"documentURL":"https://2captcha.com/demo/mtcaptcha","frameId":"E3B88E14D3DE8080562B427A99C04BB8","hasUserGesture":false,"initiator":{"columnNumber":4,"lineNumber":330,"type":"parser","url":"https://2captcha.com/demo/mtcaptcha"},"loaderId":"679A5A918ECFC3434BB2D84F13068C4C","redirectHasExtraInfo":false,"request":{"headers":{"Accept-Language":"en-US,en;q=0.9","Origin":"https://2captcha.com","Referer":"https://2captcha.com/demo/mtcaptcha","User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\""},"initialPriority":"VeryHigh","isSameSite":true,"method":"GET","mixedContentType":"none","referrerPolicy":"strict-origin-when-cross-origin","url":"https://2captcha.com/dist/web/assets/manrope-4qdASHmp.woff2"},"requestId":"48945.32","timestamp":531918.960398,"type":"Font","wallTime":1748891418.156582}
00:55:18.19940400 CA5340011D717DFA44E20420F0102943         Network.resourceChangedPriority {"newPriority":"High","requestId":"48945.9","timestamp":531918.996378}
00:55:18.19942900 CA5340011D717DFA44E20420F0102943         Network.resourceChangedPriority {"newPriority":"High","requestId":"48945.236","timestamp":531918.996386}
00:55:18.19944800 CA5340011D717DFA44E20420F0102943         Network.resourceChangedPriority {"newPriority":"High","requestId":"48945.8","timestamp":531918.996389}
00:55:18.19951000 CA5340011D717DFA44E20420F0102943      Network.requestWillBeSentExtraInfo {"associatedCookies":[{"blockedReasons":[],"cookie":{"domain":".2captcha.com","expires":1780427417.834715,"httpOnly":false,"name":"i18next","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":9,"sourcePort":443,"sourceScheme":"Secure","value":"en"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836307,"httpOnly":false,"name":"guest_currency","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":false,"session":false,"size":17,"sourcePort":443,"sourceScheme":"Secure","value":"usd"},"exemptionReason":"None"},{"blockedReasons":[],"cookie":{"domain":"2captcha.com","expires":1783451417.836399,"httpOnly":false,"name":"user_country","path":"/","priority":"Medium","sameParty":false,"sameSite":"Lax","secure":true,"session":false,"size":14,"sourcePort":443,"sourceScheme":"Secure","value":"np"},"exemptionReason":"None"}],"clientSecurityState":{"initiatorIPAddressSpace":"Public","initiatorIsSecureContext":true,"privateNetworkRequestPolicy":"PreflightWarn"},"connectTiming":{"requestTime":531919.002758},"headers":{":authority":"2captcha.com",":method":"GET",":path":"/dist/web/assets/manrope-4qdASHmp.woff2",":scheme":"https","accept":"*/*","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9","cookie":"i18next=en; guest_currency=usd; user_country=np","origin":"https://2captcha.com","priority":"u=0","referer":"https://2captcha.com/demo/mtcaptcha","sec-ch-ua":"\"Chromium\";v=\"135\", \"Not-A.Brand\";v=\"8\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","sec-fetch-dest":"font","sec-fetch-mode":"cors","sec-fetch-site":"same-origin","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"48945.32","siteHasCookieInOtherPartition":false}
00:55:18.19976900 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":5880,"encodedDataLength":0,"requestId":"48945.8","timestamp":531919.002604}
00:55:18.19980300 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":0,"encodedDataLength":2309,"requestId":"48945.8","timestamp":531919.003241}
00:55:18.19982200 CA5340011D717DFA44E20420F0102943                 Network.loadingFinished {"encodedDataLength":3101,"requestId":"48945.8","timestamp":531918.988128}
00:55:18.19989000 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":4941,"encodedDataLength":0,"requestId":"48945.6","timestamp":531919.003355}
00:55:18.20026000 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":424,"encodedDataLength":0,"requestId":"48945.9","timestamp":531919.003441}
00:55:18.20030500 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":0,"encodedDataLength":235,"requestId":"48945.9","timestamp":531919.003823}
00:55:18.20033400 CA5340011D717DFA44E20420F0102943                 Network.loadingFinished {"encodedDataLength":990,"requestId":"48945.9","timestamp":531918.996636}
00:55:18.20041800 CA5340011D717DFA44E20420F0102943                    Network.dataReceived {"dataLength":1828,"encodedDataLength":0,"requestId":"48945.236","timestamp":531919.003903}
00:55:18.20056800 CA5340011D717DFA44E20420F0102943                 Network.loadingFinished {"encodedDataLength":33021,"requestId":"679A5A918ECFC3434BB2D84F13