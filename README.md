# Browser Session Manager

A tool for managing browser sessions with <PERSON><PERSON><PERSON><PERSON>. This script allows you to:

1. Create and save browser sessions (cookies, localStorage, sessionStorage)
2. Inject saved sessions into new browser instances
3. Work with multiple platforms (GitHub, Facebook)

## What This Demonstrates

This script demonstrates several important concepts for browser automation and testing:

### 1. Session Management

- **Capturing Session Data**: The script captures all relevant session data including cookies, localStorage, and sessionStorage.
- **Persisting Sessions**: Session data is saved to disk for later use.
- **Session Injection**: Saved sessions can be injected into new browser instances, allowing you to bypass login screens.

### 2. Cross-Platform Support

- **Configurable Platforms**: The script supports multiple platforms (GitHub, Facebook) and can be easily extended.
- **Platform-Specific Selectors**: Each platform has its own configuration for login URLs, selectors, and demo pages.

### 3. Authentication Handling

- **Manual Authentication**: The script handles complex authentication flows by allowing manual login.
- **2FA/OTP Support**: Works with two-factor authentication by waiting for you to complete all authentication steps.
- **Login Verification**: Verifies successful login before saving session data.

## How It Works

### Session Creation

1. The script launches a browser and navigates to the login page
2. You manually log in (including any 2FA/OTP steps)
3. The script verifies successful login
4. All session data (cookies, localStorage, sessionStorage) is captured and saved
5. A screenshot is taken as proof of login

### Session Injection

1. The script loads saved session data
2. A new browser is launched
3. Cookies, localStorage, and sessionStorage are injected
4. The browser navigates to the platform's homepage
5. The script verifies that the session is active
6. You can optionally see a demonstration of the session

## Usage

### Installation

```bash
npm install puppeteer
```

### Running the Script

To create or use a GitHub session:

```bash
node quick-session-inject-clean.js github
```

To create or use a Facebook session:

```bash
node quick-session-inject-clean.js facebook
```

To see help:

```bash
node quick-session-inject-clean.js --help
```

### Session Storage

Sessions are stored in the `sessions` directory, organized by platform:

```
sessions/
├── github/
│   ├── cookies.json
│   ├── localStorage.json
│   ├── sessionStorage.json
│   ├── username.txt
│   └── login_proof.png
│   └── ...
└── facebook/
    └── ...
```

## Extending the Script

### Adding a New Platform

To add support for a new platform, add a new entry to the `PLATFORMS` object:

```javascript
newplatform: {
  name: "New Platform",
  loginUrl: "https://newplatform.com/login",
  homeUrl: "https://newplatform.com/",
  loginSelector: '.user-profile-element',
  demoUrls: [
    { url: "https://newplatform.com/dashboard", name: "Dashboard" },
    { url: "https://newplatform.com/settings", name: "Settings" },
  ],
},
```

## Use Cases

- **Automated Testing**: Skip login steps in automated tests
- **Development**: Quickly access authenticated sections of websites
- **Debugging**: Reproduce issues that only occur in authenticated sessions
- **Session Analysis**: Examine what data is stored in browser sessions

## Notes

- This script is for educational and development purposes only
- Always respect website terms of service and privacy policies
- The script uses a visible browser window to allow manual interaction
- Sessions may expire based on the platform's security policies
