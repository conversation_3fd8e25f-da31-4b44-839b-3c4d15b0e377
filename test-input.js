import { createTarget, CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function testInputDispatching() {
  console.log("=== Testing Input Dispatching ===");

  try {
    // Create target for a page with forms
    const url = "https://httpbin.org/forms/post";
    console.log(`Creating target for form page: ${url}`);
    const targetInfo = await createTarget(url);
    const cdpInstance = new CDP(targetInfo);

    // Enable necessary domains
    await cdpInstance.Runtime.enable();
    await cdpInstance.Page.enable();
    await cdpInstance.DOM.enable();
    console.log("All domains enabled for input dispatching");

    // Wait for page to load
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Get page information
    const pageInfo = await cdpInstance.Runtime.evaluate({
      expression: `JSON.stringify({
        title: document.title,
        url: window.location.href,
        formElements: {
          inputs: document.querySelectorAll('input').length,
          textareas: document.querySelectorAll('textarea').length,
          buttons: document.querySelectorAll('button, input[type="submit"]').length
        },
        allInputs: Array.from(document.querySelectorAll('input')).map(input => ({
          name: input.name,
          type: input.type,
          id: input.id,
          placeholder: input.placeholder
        }))
      })`,
    });
    const pageData = pageInfo.result.value
      ? JSON.parse(pageInfo.result.value)
      : null;
    console.log("Page loaded:", pageData);

    // Demonstrate mouse click on first input field
    console.log("\n--- Mouse Click Example ---");
    const inputElement = await cdpInstance.Runtime.evaluate({
      expression: `
        JSON.stringify((() => {
          // Try multiple selectors to find an input field
          const selectors = [
            'input[name="custname"]',
            'input[type="text"]',
            'input:not([type="hidden"]):not([type="submit"]):not([type="button"])',
            'input'
          ];

          let input = null;
          for (const selector of selectors) {
            input = document.querySelector(selector);
            if (input) break;
          }

          if (input) {
            const rect = input.getBoundingClientRect();
            return {
              found: true,
              x: rect.left + rect.width / 2,
              y: rect.top + rect.height / 2,
              name: input.name || 'unnamed',
              type: input.type || 'text',
              selector: selectors.find(s => document.querySelector(s) === input)
            };
          } else {
            return { found: false, availableInputs: document.querySelectorAll('input').length };
          }
        })())
      `,
    });

    const inputData = inputElement.result.value
      ? JSON.parse(inputElement.result.value)
      : null;
    console.log("Input element search result:", inputData);

    if (inputData && inputData.found) {
      const { x, y, name, type, selector } = inputData;
      console.log(
        `Found input field: ${name} (${type}) at position (${x}, ${y}) using selector: ${selector}`
      );

      // Click on the input field
      await cdpInstance.Input.dispatchMouseEvent({
        type: "mousePressed",
        x: x,
        y: y,
        button: "left",
        clickCount: 1,
      });

      await cdpInstance.Input.dispatchMouseEvent({
        type: "mouseReleased",
        x: x,
        y: y,
        button: "left",
        clickCount: 1,
      });

      console.log("Mouse click dispatched successfully");

      // Wait a moment
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Demonstrate keyboard input
      console.log("\n--- Keyboard Input Example ---");
      const textToType = "John Doe";
      console.log(`Typing text: "${textToType}"`);

      // Type each character
      for (const char of textToType) {
        await cdpInstance.Input.dispatchKeyEvent({
          type: "keyDown",
          text: char,
        });
        await cdpInstance.Input.dispatchKeyEvent({
          type: "keyUp",
          text: char,
        });
        // Small delay between keystrokes for realism
        await new Promise((resolve) => setTimeout(resolve, 50));
      }

      console.log("Text typed successfully");

      // Verify the input was filled
      const inputValue = await cdpInstance.Runtime.evaluate({
        expression: `
          const input = document.querySelector('input[type="text"]') || 
                       document.querySelector('input:not([type="hidden"]):not([type="submit"]):not([type="button"])') ||
                       document.querySelector('input');
          input ? input.value : 'No input found'
        `,
      });
      console.log("Input field value:", inputValue.result.value);
    } else {
      const debugInfo = inputElement.result.value || {};
      console.log(
        `Input field not found. Available inputs: ${
          debugInfo.availableInputs || "unknown"
        }`
      );
    }

    console.log("Input dispatching test completed successfully");
  } catch (error) {
    console.error("Error in input dispatching test:", error);
  }
}

testInputDispatching().catch(console.error);
