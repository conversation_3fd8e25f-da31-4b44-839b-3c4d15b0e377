!function(t){"function"==typeof define&&define.amd?define(t):t()}(function(){"use strict";function t(t,e,n,s){if("a"===n&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!s:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?s:"a"===n?s.call(t):s?s.value:e.get(t)}function e(t,e,n,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(t,n):r?r.value=n:e.set(t,n),n}var n,s,r,o,i,a,c,h,f;"function"==typeof SuppressedError&&SuppressedError;const l=void 0,d="ConnectionRefused",w="GET",g={apiUrl:"http://localhost:9222",apiPath:"json/version",apiPathTargets:"json",apiPathNewTarget:"json/new",apiPathActivateTarget:"json/activate",apiPathCloseTarget:"json/close",connectionMaxRetry:20,connectionRetryDelay:500};class u{constructor(e){n.set(this,void 0),s.set(this,Object.assign({},e)),r.set(this,new Map),Object.assign(t(this,s,"f"),e);return["Runtime","Target","Page","Console","Network","Input","DOM","CSS","Debugger","Profiler","HeapProfiler","Security","ServiceWorker","Storage","SystemInfo","Browser","Emulation","Animation","Accessibility"].forEach(t=>{this[t]=this.createDomain(t)}),new Proxy(this,{get:(t,e)=>"string"==typeof e?(e in t||(t[e]=t.createDomain(e)),t[e]):t[e]})}createDomain(t){const e=this;return new Proxy(Object.create(null),{get(n,s){if("string"==typeof s)return"addEventListener"===s?e.getDomainListenerFunction("addEventListener",t):"removeEventListener"===s?e.getDomainListenerFunction("removeEventListener",t):(n[s]||(n[s]=e.getDomainMethodFunction(s,t)),n[s])}})}getDomainMethodFunction(e,s){const o=this;return async(i={},a)=>{await o.ready();const c=t(o,r,"f").get(s);if(c!==l){for(;c.length>0;){const e=c.shift();e&&t(o,n,"f")&&t(o,n,"f")[e.methodName](`${e.domainName}.${e.type}`,e.listener)}t(o,r,"f").delete(s)}if(!t(o,n,"f"))throw new Error("Connection not established");const h=i||{};return t(o,n,"f").sendMessage(`${s}.${e}`,h,a)}}getDomainListenerFunction(e,s){const o=this;return(i,a)=>{if(t(o,n,"f")===l){let n=t(o,r,"f").get(s);n===l&&(n=[],t(o,r,"f").set(s,n)),n.push({methodName:e,domainName:s,type:i,listener:a})}else t(o,n,"f")[e](`${s}.${i}`,a)}}async ready(){if(t(this,n,"f")===l){let r=t(this,s,"f").webSocketDebuggerUrl;if(r===l){const e=new URL(t(this,s,"f").apiPath,t(this,s,"f").apiUrl);r=(await y(e,t(this,s,"f"))).webSocketDebuggerUrl}const o=new m(r);await o.open(),e(this,n,o,"f")}}get options(){return t(this,s,"f")}set options(e){Object.assign(t(this,s,"f"),e)}get connection(){if(!t(this,n,"f"))throw new Error("Connection not established. Call a CDP method first to establish connection.");return t(this,n,"f")}reset(){t(this,n,"f")!==l&&(t(this,n,"f").close(),e(this,n,l,"f"),t(this,r,"f").clear())}static getTargets(){const{apiPathTargets:t,apiUrl:e}=p;return y(new URL(t,e),p)}static createTarget(t){const{apiPathNewTarget:e,apiUrl:n}=p;return y(new URL(t?`${e}?${t}`:e,n),p,"PUT")}static async activateTarget(t){const{apiPathActivateTarget:e,apiUrl:n}=p;await y(new URL(`${e}/${t}`,n),p,w,!1)}static async closeTarget(t){const{apiPathCloseTarget:e,apiUrl:n}=p;await y(new URL(`${e}/${t}`,n),p,w,!1)}}n=new WeakMap,s=new WeakMap,r=new WeakMap;const p=Object.assign({},g);new u(p);class m extends EventTarget{constructor(t){super(),o.add(this),i.set(this,void 0),a.set(this,void 0),c.set(this,new Map),h.set(this,0),e(this,i,t,"f")}open(){return e(this,a,new WebSocket(t(this,i,"f")),"f"),t(this,a,"f").addEventListener("message",e=>t(this,o,"m",f).call(this,JSON.parse(e.data))),new Promise((e,n)=>{t(this,a,"f").addEventListener("open",()=>e()),t(this,a,"f").addEventListener("close",t=>n(new Error(t.reason))),t(this,a,"f").addEventListener("error",()=>n(new Error))})}sendMessage(n,s={},r){if(!t(this,a,"f"))throw new Error("WebSocket not connected");const o=t(this,h,"f"),i=JSON.stringify({id:o,method:n,params:s,sessionId:r});let f;e(this,h,(t(this,h,"f")+1)%Number.MAX_SAFE_INTEGER,"f"),t(this,a,"f").send(i);const l=new Promise((t,e)=>f={resolve:t,reject:e,method:n,params:s,sessionId:r});return t(this,c,"f").set(o,f),l}close(){t(this,a,"f")&&t(this,a,"f").close()}}function y(t,e,n=w,s=!0){return v(async()=>{let e;try{e=await fetch(t,{method:n})}catch(t){const e=t;throw e.code=d,e}if(e.status>=400){const t=new Error(e.statusText||`HTTP Error ${e.status}`);throw t.status=e.status,t.code="ConnectionError",t}return s?e.json():e.text()},e)}async function v(t,e,n=0){const{connectionMaxRetry:s,connectionRetryDelay:r}=e;try{return await t()}catch(o){if(o.code===d&&n<s)return await new Promise(t=>setTimeout(t,r)),v(t,e,n+1);throw o}}i=new WeakMap,a=new WeakMap,c=new WeakMap,h=new WeakMap,o=new WeakSet,f=function({id:e,method:n,result:s,error:r,params:o,sessionId:i}){if(e!==l){const n=t(this,c,"f").get(e);if(n){const{resolve:o,reject:i}=n;if(r===l)o(s);else{const t=r.message+" when calling "+`${n.method}(${JSON.stringify(n.params)})`+(n.sessionId===l?"":` (sessionId ${JSON.stringify(n.sessionId)})`),e=new Error(t);e.code=r.code,i(e)}t(this,c,"f").delete(e)}}if(n!==l){const t=new Event(n);t.params=o,t.sessionId=i,this.dispatchEvent(t)}},function(){let t,e=null;function n(...t){console.log("[browserController]",...t)}globalThis.browserController={init:async function(s,r){n("Connecting to CDP and attaching to target:",r),await async function(s,r){try{e=new u({webSocketDebuggerUrl:s}),n("Attaching to target:",r);const o=new Promise(n=>{const s=({params:o})=>{const{sessionId:i,targetInfo:a}=o;a.targetId===r&&(t=i,null==e||e.Target.removeEventListener("attachedToTarget",s),n(i))};null==e||e.Target.addEventListener("attachedToTarget",s)});await e.Target.attachToTarget({targetId:r,flatten:!0}),await o,e=null,n("✓ Browser controller attached to target",r,"with sessionId:",t)}catch(t){throw function(...t){console.error("[browserController]",...t)}("Failed to connect to CDP and attach to target:",t),t}}(s,r),n("Browser controller initialized with sessionId:",t)}}}()});
