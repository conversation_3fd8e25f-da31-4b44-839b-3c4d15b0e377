import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";
import fs from "fs";
import { Browserbase } from "@browserbasehq/sdk";

const BROWSERBASE_API_KEY = "bb_live_QwIt78tzGs_zl31pDrEpnMCZ-lI";
const BROWSERBASE_PROJECT_ID = "8965aab4-7075-4993-9f3b-244157f0bd92";

(async () => {
  let browser;
  let wsEndpoint;
  const browserType = "hyperbrowser";
  const useLocalBrowser = false;
  const launchNew = true;
  if (useLocalBrowser) {
    if (launchNew) {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [
          "--remote-debugging-port=9222",
          "--remote-allow-origins=*",
          "--no-first-run",
          "--auto-accept-this-tab-capture",
          "--no-default-browser-check",
        ],
      });
    } else {
      const res = await fetch("http://localhost:9222/json/version");
      const { webSocketDebuggerUrl } = await res.json();

      browser = await puppeteer.connect({
        browserWSEndpoint: webSocketDebuggerUrl,
      });
    }
  } else {
    if (browserType == "browserbase") {
      const bb = new Browserbase({
        apiKey: BROWSERBASE_API_KEY,
      });
      const session = await bb.sessions.create({
        projectId: BROWSERBASE_PROJECT_ID,
      });
      wsEndpoint = session.connectUrl;
    } else if (browserType == "hyperbrowser") {
      const hyperBrowser = new Hyperbrowser({
        apiKey: "hb_28aac10409666bbccf859a9b8804",
        timeout: 60000,
      });
      const session = await hyperBrowser.sessions.create({
        browserArgs: ["--auto-accept-this-tab-capture"],
        device: ["desktop"],
      });
      wsEndpoint = session.wsEndpoint;
    }

    browser = await puppeteer.connect({
      browserWSEndpoint: wsEndpoint,
    });
  }
  console.log({ wsEndpoint });

  const page = await browser.newPage();
  await page.setBypassCSP(true);
  // Simple error monitoring - just console logs
  page.on("error", (error) => {
    console.error("💥 PAGE CRASH:", error.message);
  });

  page.on("pageerror", (error) => {
    console.error("💥 SCRIPT ERROR:", error.message);
  });

  // Log browser console messages
  page.on("console", (msg) => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️ BROWSER [${type.toUpperCase()}]: ${text}`);
  });

  console.log("1️⃣ Navigating to github...");
  await page.goto("https://github.com/password_reset", {
    waitUntil: "domcontentloaded",
  });

  console.log("2️⃣ Starting screen sharing... in 4000ms");
  await new Promise((resolve) => setTimeout(resolve, 4000));
  const script = fs.readFileSync("./browser-controller.min.js", "utf8");
  await page.evaluate(script);
  const _wsEndpoint = browser.wsEndpoint();
  const _targetId =
    page.target()._targetId || page.target()._targetInfo?.targetId;

  await page.evaluate(
    (ws, target) => {
      window.browserController.init(ws, target);
    },
    _wsEndpoint,
    _targetId
  );

  // Wait for screen sharing to stabilize
  await new Promise((resolve) => setTimeout(resolve, 2000));
})();
