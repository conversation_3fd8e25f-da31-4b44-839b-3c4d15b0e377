<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Clean Iframe Test Page</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f0f0f0;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
      }
      .iframe-section {
        margin: 20px 0;
        padding: 15px;
        border: 2px solid #333;
        border-radius: 8px;
        background-color: white;
      }
      iframe {
        width: 100%;
        height: 200px;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
      h2 {
        color: #333;
        margin-top: 0;
      }
      .main-input {
        width: 300px;
        padding: 10px;
        margin: 10px 0;
        border: 1px solid #ccc;
        border-radius: 4px;
      }
      .loading {
        color: #666;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Clean Iframe Test Page</h1>

      <div class="iframe-section">
        <h2>Main Page Input</h2>
        <input
          type="text"
          class="main-input"
          placeholder="Main page input field"
          id="main-input"
        />
      </div>

      <div class="iframe-section">
        <h2>Iframe 1: Simple Same Domain</h2>
        <iframe
          id="iframe1"
          src="data:text/html,<html><body><h3>Iframe 1</h3><input type='text' placeholder='Input 1' id='input1'><script>console.log('Iframe 1 loaded');</script></body></html>"
        ></iframe>
      </div>

      <div class="iframe-section">
        <h2>Iframe 2: Form Elements</h2>
        <iframe
          id="iframe2"
          src="data:text/html,<html><body><h3>Form Iframe</h3><input type='text' placeholder='Name' id='name'><input type='email' placeholder='Email' id='email'><select id='country'><option>USA</option><option>Canada</option></select><textarea placeholder='Comments' id='comments'></textarea><script>console.log('Form iframe loaded');</script></body></html>"
        ></iframe>
      </div>

      <div class="iframe-section">
        <h2>Iframe 3: Cross Domain (httpbin.org)</h2>
      </div>

      <div class="iframe-section">
        <h2>Iframe 3: 2captcha</h2>
        <iframe
          id="iframe3"
          src="https://httpbin.org/html"
          sandbox="allow-scripts allow-same-origin"
        ></iframe>
      </div>

      <div class="iframe-section">
        <h2>Iframe 4: About:blank (Dynamic Content)</h2>
        <iframe id="iframe4" src="about:blank"></iframe>
      </div>

      <div class="iframe-section">
        <h2>Iframe 5: Delayed Load (2 seconds)</h2>
        <div id="loading5" class="loading">
          Loading iframe 5 in 2 seconds...
        </div>
        <iframe id="iframe5" style="display: none"></iframe>
      </div>

      <div class="iframe-section">
        <h2>Iframe 6: Srcdoc Attribute</h2>
        <iframe
          id="iframe6"
          srcdoc="<html><body><h3>Srcdoc Iframe</h3><input type='text' placeholder='Srcdoc input' id='srcdoc-input'><input type='number' placeholder='Number' id='number-input'><script>console.log('Srcdoc iframe loaded');</script></body></html>"
        ></iframe>
      </div>
      <html>
        <head>
          <title>
            reCAPTCHA demo: Explicit render after an onload callback
          </title>
          <script type="text/javascript">
            var onloadCallback = function () {
              grecaptcha.render("html_element", {
                sitekey: "your_site_key",
              });
            };
          </script>
        </head>
        <body>
          <form action="?" method="POST">
            <div id="html_element"></div>
            <br />
            <input type="submit" value="Submit" />
          </form>
          <script
            src="https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit"
            async
            defer
          ></script>
        </body>
      </html>
      <div class="iframe-section">
        <h2>Iframe 7: Blob URL Content</h2>
        <iframe id="iframe7"></iframe>
      </div>

      <div class="iframe-section">
        <h2>Iframe 8: Document.write Created (3 seconds)</h2>
        <div id="loading8" class="loading">
          Creating iframe via document.write in 3 seconds...
        </div>
        <div id="iframe8-container"></div>
      </div>
    </div>

    <script>
      console.log("Main page script executed");

      // Add content to about:blank iframe (iframe4) after 1 second
      setTimeout(function () {
        const iframe4 = document.getElementById("iframe4");
        const doc = iframe4.contentDocument || iframe4.contentWindow.document;

        doc.open();
        doc.write(
          '<html><body><h3>Dynamic Content</h3><input type="text" placeholder="Dynamic input" id="dynamic-input"><input type="radio" name="radio" id="radio1"> <label for="radio1">Option 1</label></body></html>'
        );
        doc.close();
        console.log("Dynamic iframe content added");
      }, 1000);

      // Load iframe 5 after 2 seconds
      setTimeout(function () {
        const iframe5 = document.getElementById("iframe5");
        const loading5 = document.getElementById("loading5");

        iframe5.src =
          "data:text/html,<html><body><h3>Delayed Iframe</h3><input type='text' placeholder='Delayed input' id='delayed-input'><input type='checkbox' id='checkbox'> <label for='checkbox'>Check me</label></body></html>";

        iframe5.style.display = "block";
        loading5.style.display = "none";
        console.log("Iframe 5 loaded after delay");
      }, 2000);

      // Create blob URL iframe (iframe7) after 0.5 seconds
      setTimeout(function () {
        const iframe7 = document.getElementById("iframe7");

        const htmlContent =
          '<html><body><h3>Blob URL Iframe</h3><input type="text" placeholder="Blob input" id="blob-input"><input type="date" id="date-input"></body></html>';

        const blob = new Blob([htmlContent], { type: "text/html" });
        const blobUrl = URL.createObjectURL(blob);
        iframe7.src = blobUrl;

        console.log("Iframe 7 loaded with blob URL");
      }, 500);

      // Create iframe 8 via innerHTML after 3 seconds
      setTimeout(function () {
        const container = document.getElementById("iframe8-container");
        const loading8 = document.getElementById("loading8");

        container.innerHTML =
          "<iframe id=\"iframe8-dynamic\" src=\"data:text/html,<html><body><h3>Created Iframe</h3><input type='text' placeholder='Created input' id='created-input'><input type='color' id='color-input'></body></html>\" style=\"width: 100%; height: 200px; border: 1px solid #ccc; border-radius: 4px;\"></iframe>";

        loading8.style.display = "none";
        console.log("Iframe 8 created via innerHTML");
      }, 3000);

      document.addEventListener("DOMContentLoaded", function () {
        console.log("Main page DOMContentLoaded fired");
      });
    </script>
  </body>
</html>
