/**
 * pupcdp_fixed.js - CDP-based script injection mimicking Puppeteer FrameManager
 *
 * This implementation follows the exact Puppeteer FrameManager approach for handling
 * iframe script injection. Key improvements:
 *   1) Proper session management for iframe targets
 *   2) Script injection into all existing frames (like FrameManager.evaluateOnNewDocument)
 *   3) Proper target attachment handling with script injection
 *   4) Frame tracking per session like Puppeteer's FrameManager
 */

const puppeteer = require("puppeteer");

(async () => {
  // 1) Launch Chrome with remote-debugging enabled
  const browser = await puppeteer.launch({
    headless: false,
    args: ["--remote-debugging-port=9222"],
    defaultViewport: null,
  });

  // 2) Grab the WebSocket URL from Chrome's /json/version
  const versionJson = await fetch("http://localhost:9223/json/version").then(
    (r) => r.json()
  );
  const { webSocketDebuggerUrl } = versionJson;

  // 3) Connect Puppeteer to that WS endpoint
  const connected = await puppeteer.connect({
    browserWSEndpoint: webSocketDebuggerUrl,
  });

  // 4) Open a new blank page and get its "main" CDP session
  const page = await connected.newPage();
  const mainSession = await page.createCDPSession();

  // 5) The injection script
  const scriptSource = 'console.log("hii from new page", document.URL);';

  // A single world name we'll use everywhere
  const WORLD_NAME = "custom-world";

  // Track sessions and their frames (mimicking FrameManager)
  const sessions = new Map(); // sessionId -> session
  const frameToSession = new Map(); // frameId -> sessionId
  const scriptsToEvaluate = new Set(); // Track registered scripts
  const isolatedWorlds = new Set(); // Track created isolated worlds

  // Register the main session
  sessions.set(mainSession.id(), mainSession);

  // Helper function to setup event listeners for a session (like FrameManager.setupEventListeners)
  function setupEventListeners(session) {
    console.log(`Setting up event listeners for session: ${session.id()}`);

    session.on("Page.frameAttached", async (event) => {
      const { frameId, parentFrameId } = event;
      console.log(`Frame attached: ${frameId} (parent: ${parentFrameId})`);
      frameToSession.set(frameId, session.id());
    });

    session.on("Page.frameNavigated", async (event) => {
      const { frame } = event;
      const { id: frameId, url } = frame;
      console.log(`Frame navigated: ${frameId} -> ${url}`);
      frameToSession.set(frameId, session.id());

      // After frame navigation, create isolated world for this frame
      await createIsolatedWorldForFrame(session, frameId, WORLD_NAME);
    });

    session.on("Page.frameDetached", (event) => {
      const { frameId } = event;
      console.log(`Frame detached: ${frameId}`);
      frameToSession.delete(frameId);
    });
  }

  // Helper function to create isolated world for a specific frame (mimicking FrameManager.#createIsolatedWorld)
  async function createIsolatedWorldForFrame(session, frameId, worldName) {
    const key = `${session.id()}:${worldName}`;

    if (!isolatedWorlds.has(key)) {
      // First time for this session - add script to evaluate on new document
      try {
        await session.send("Page.addScriptToEvaluateOnNewDocument", {
          source: scriptSource,
          worldName,
        });
        console.log(
          `Added script to evaluate on new document for session: ${session.id()}`
        );
      } catch (error) {
        console.error(
          `Failed to add script for session ${session.id()}:`,
          error.message
        );
      }
      isolatedWorlds.add(key);
    }

    // Create isolated world for this specific frame
    try {
      await session.send("Page.createIsolatedWorld", {
        frameId,
        worldName,
        grantUniversalAccess: true,
      });
      console.log(
        `Created isolated world for frame: ${frameId} in session: ${session.id()}`
      );
    } catch (error) {
      console.error(
        `Failed to create isolated world for frame ${frameId}:`,
        error.message
      );
    }
  }

  // Function to inject scripts into all existing frames (mimicking FrameManager.evaluateOnNewDocument)
  async function evaluateOnNewDocument(source) {
    console.log(
      "→ Setting up script injection (mimicking FrameManager.evaluateOnNewDocument)"
    );

    // Add script to main session first
    const { identifier } = await mainSession.send(
      "Page.addScriptToEvaluateOnNewDocument",
      {
        source,
      }
    );
    console.log(`Main session script identifier: ${identifier}`);

    scriptsToEvaluate.add(source);

    // Now inject into all existing frames across all sessions (like FrameManager does)
    for (const [sessionId, session] of sessions) {
      console.log(`Processing session: ${sessionId}`);

      // Get all frames for this session
      try {
        const { frameTree } = await session.send("Page.getFrameTree");
        await processFrameTree(session, frameTree);
      } catch (error) {
        console.error(
          `Failed to get frame tree for session ${sessionId}:`,
          error.message
        );
      }
    }
  }

  // Process frame tree recursively (like FrameManager.#handleFrameTree)
  async function processFrameTree(session, frameTree) {
    const frame = frameTree.frame;
    frameToSession.set(frame.id, session.id());

    console.log(`Processing frame: ${frame.id} (${frame.url})`);

    // Create isolated world for this frame
    await createIsolatedWorldForFrame(session, frame.id, WORLD_NAME);

    // Process child frames recursively
    if (frameTree.childFrames) {
      for (const childFrame of frameTree.childFrames) {
        await processFrameTree(session, childFrame);
      }
    }
  }

  // Handle new target attachment (like FrameManager.onAttachedToTarget)
  async function onAttachedToTarget(targetInfo, session) {
    console.log(
      `Target attached: ${targetInfo.targetId} (type: ${targetInfo.type})`
    );

    if (targetInfo.type !== "iframe") {
      return;
    }

    // Register the new session
    sessions.set(session.id(), session);

    // Setup event listeners for the new session
    setupEventListeners(session);

    // Initialize the session (like FrameManager.initialize)
    await initializeSession(session);

    // Inject existing scripts into this new session
    for (const script of scriptsToEvaluate) {
      try {
        await session.send("Page.addScriptToEvaluateOnNewDocument", {
          source: script,
          worldName: WORLD_NAME,
        });
        console.log(
          `Injected existing script into new iframe session: ${session.id()}`
        );
      } catch (error) {
        console.error(
          `Failed to inject script into new session:`,
          error.message
        );
      }
    }
  }

  // Initialize a session (like FrameManager.initialize)
  async function initializeSession(session) {
    try {
      await Promise.all([
        session.send("Page.enable"),
        session.send("Runtime.enable"),
      ]);
      console.log(`Initialized session: ${session.id()}`);
    } catch (error) {
      console.error(
        `Failed to initialize session ${session.id()}:`,
        error.message
      );
    }
  }

  // Setup main session
  console.log("→ Setting up main session...");
  setupEventListeners(mainSession);
  await initializeSession(mainSession);

  // Setup autoAttach for iframe targets (like FrameManager)
  console.log("→ Setting up autoAttach for iframe targets...");
  await mainSession.send("Target.setAutoAttach", {
    autoAttach: true,
    waitForDebuggerOnStart: false,
    flatten: true,
  });

  // Listen for target attached events
  mainSession.on("Target.attachedToTarget", async (event) => {
    const { targetInfo, sessionId } = event;
    console.log(
      `Target.attachedToTarget event: ${targetInfo.targetId}, sessionId: ${sessionId}`
    );

    // For iframe targets, we need to get the session differently
    if (targetInfo.type === "iframe") {
      // The sessionId in the event is the session ID we can use
      // We need to create a session wrapper that can send commands
      const iframeSession = {
        id: () => sessionId,
        send: (method, params) => {
          return mainSession.send("Target.sendMessageToTarget", {
            sessionId,
            message: JSON.stringify({ id: Date.now(), method, params }),
          });
        },
        on: () => {}, // Simplified for now
      };

      await onAttachedToTarget(targetInfo, iframeSession);
    }
  });

  // Now inject the script using our FrameManager-like approach
  await evaluateOnNewDocument(scriptSource);

  // Navigate to the test page
  console.log("→ Navigating to https://2captcha.com/demo/mtcaptcha ...");
  await mainSession.send("Page.navigate", {
    url: "https://2captcha.com/demo/mtcaptcha",
  });

  await new Promise((resolve) =>
    mainSession.once("Page.loadEventFired", resolve)
  );

  console.log(
    "✓ Loaded. Script should now be injected in main frame and all iframes."
  );
  console.log(
    "Check the console for 'hii from new page' messages from different frames."
  );
})();
