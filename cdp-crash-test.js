/**
 * Simple CDP Script - Browser automation using simple-cdp package
 *
 * This script demonstrates how to use the simple-cdp package to:
 * 1. Connect to a Chrome browser with remote debugging enabled
 * 2. Navigate to a webpage
 * 3. Execute JavaScript expressions
 * 4. Interact with page elements
 * 5. Handle auto-attached targets (iframes, new tabs, etc.)
 *
 * Prerequisites:
 * - Start Chrome with: chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 * - Or use: google-chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 */

import { CDP } from "simple-cdp";
import WebSocket from "ws";
import { Hyperbrowser } from "@hyperbrowser/sdk";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;
let cdp;
async function advancedExample() {
  console.log("\n=== Advanced Example with Auto-Attach ===");

  try {
    const hyperBrowser = new Hyperbrowser({
      apiKey: "hb_28aac10409666bbccf859a9b8804",
      timeout: 60000,
    });

    // Create session with screen capture args
    console.log("🔧 Creating Hyperbrowser session...");
    const session = await hyperBrowser.sessions.create({
      browserArgs: ["--auto-accept-this-tab-capture"],
      device: ["desktop"],
    });

    cdp = new CDP({ webSocketDebuggerUrl: session.wsEndpoint });
    // Enable auto-attach to new targets
    await cdp.Target.setAutoAttach({
      autoAttach: true,
      flatten: true,
      waitForDebuggerOnStart: false,
    });
    console.log("Auto-attach enabled");

    // Add event listener for attached targets
    cdp.Target.addEventListener("attachedToTarget", onAttachedToTarget);
    console.log("Event listener added for attachedToTarget");

    // Create a new target and navigate to a test page
    const url = "https://google.com";
    console.log(`Creating target for: ${url}`);
    await cdp.Target.createTarget({ url });
    console.log("Advanced example setup completed");
  } catch (error) {
    console.error("Error in advanced example:", error);
  }
}

async function onAttachedToTarget({ params }) {
  console.log("Target attached:", params.targetInfo);

  try {
    const { sessionId, targetInfo } = params;

    if (targetInfo.type === "page") {
      console.log(`Processing page target: ${targetInfo.url}`);
      await cdp.Runtime.enable(null, sessionId);
      console.log("Runtime enabled for session:", sessionId);
      await cdp.Page.enable(null, sessionId);
      console.log("Page domain enabled for session:", sessionId);
      await new Promise((resolve) => setTimeout(resolve, 5000));

      const expression = `
      (async function() {
        console.log("🎥 Requesting screen sharing...");
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            frameRate: 22,
            width: window.width,
            height: window.height,
          },
          preferCurrentTab: true,
        });
        window.screenStream = screenStream;
        console.log("✅ Screen sharing started successfully");
      })();
      `;

      const { result } = await cdp.Runtime.evaluate(
        { expression: expression },
        sessionId
      );

      console.log(`Response: ${JSON.stringify(result)}`);
      await new Promise((resolve) => setTimeout(resolve, 5000));
      await cdp.Runtime.disable(null, sessionId);
      console.log("Runtime disabled for session:", sessionId);
      await cdp.Page.disable(null, sessionId);
      console.log("Page disabled for session:", sessionId);

      //disable all domains
      await cdp.Network.disable(null, sessionId);
      console.log("Network disabled for session:", sessionId);

      // const expression2 = `
      // (async function() {
      //   console.log("Stopping screen stream...");
      //     window.screenStream.getTracks().forEach((track) => {
      //       track.stop();
      //     });
      // })();
      // `;
      const expression2 = `
        ( function() {
          window.location.href = "https://www.google.com/search?q=test";
        })();
      `;
      await cdp.Runtime.evaluate({ expression: expression2 }, sessionId);
      await new Promise((resolve) => setTimeout(resolve, 4000));
    } else {
      console.log(`Skipping non-page target: ${targetInfo.type}`);
    }
  } catch (error) {
    console.error("Error in onAttachedToTarget:", error);
  }
}

async function main() {
  try {
    await advancedExample();
  } catch (error) {
    console.error("Main execution error:", error);
  }
}
main();

// caching the LLM response
// get all the text that might include PII along with corresponding XPath ( username, email, phone number, address, credit card number, social security number, date of birth, etc.)
// redactedText = redactor.redact("all the text")
// replace the text in the DOM with redactedText with the help of XPath captured
// capture screenshot and send to openAI
// replace the text in the DOM with original text with the help of XPath captured
// Continue --
