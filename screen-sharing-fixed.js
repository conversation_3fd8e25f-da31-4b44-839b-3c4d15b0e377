const puppeteer = require("puppeteer");

(async () => {
  console.log("🚀 Screen sharing with crash prevention...");

  const browser = await puppeteer.launch({
    headless: true,
    defaultViewport: null,
    args: [
      "--auto-accept-this-tab-capture",
      "--allow-running-insecure-content",
      "--disable-web-security",
    ],
  });

  const page = await browser.newPage();

  // Inject stream management utilities
  await page.evaluateOnNewDocument(() => {
    window.streamManager = {
      activeStreams: new Set(),

      // Register a stream for tracking
      registerStream: function (stream) {
        this.activeStreams.add(stream);
        console.log(`📹 Registered stream: ${stream.id}`);

        // Auto-cleanup when stream ends
        stream.addEventListener("inactive", () => {
          this.activeStreams.delete(stream);
          console.log(`🔴 Stream ${stream.id} became inactive`);
        });

        return stream;
      },

      // Stop all active streams
      stopAllStreams: function () {
        console.log(`🛑 Stopping ${this.activeStreams.size} active streams...`);

        this.activeStreams.forEach((stream) => {
          try {
            stream.getTracks().forEach((track) => {
              track.stop();
              console.log(`⏹️ Stopped track: ${track.kind} (${track.label})`);
            });
          } catch (error) {
            console.error(`❌ Error stopping stream ${stream.id}:`, error);
          }
        });

        this.activeStreams.clear();
        console.log("✅ All streams stopped");
      },

      // Get status of all streams
      getStatus: function () {
        return Array.from(this.activeStreams).map((stream) => ({
          id: stream.id,
          active: stream.active,
          tracks: stream.getTracks().map((track) => ({
            kind: track.kind,
            enabled: track.enabled,
            readyState: track.readyState,
            label: track.label,
          })),
        }));
      },
    };

    // Override getDisplayMedia to auto-register streams
    const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;
    navigator.mediaDevices.getDisplayMedia = async function (constraints) {
      const stream = await originalGetDisplayMedia.call(this, constraints);
      return window.streamManager.registerStream(stream);
    };

    // Auto-cleanup before navigation
    window.addEventListener("beforeunload", () => {
      console.log("🔄 Page unloading - cleaning up streams...");
      window.streamManager.stopAllStreams();
    });
  });

  console.log("1️⃣ Navigating to Google...");
  await page.goto("https://google.com", { waitUntil: "domcontentloaded" });

  console.log("2️⃣ Starting screen sharing...");

  const result = await page.evaluate(async () => {
    try {
      console.log("🎥 Requesting screen sharing...");

      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.innerWidth,
          height: window.innerHeight,
        },
        preferCurrentTab: true,
      });

      console.log("✅ Screen sharing started successfully");
      return {
        success: true,
        streamId: screenStream.id,
        active: screenStream.active,
        tracks: screenStream.getTracks().length,
      };
    } catch (error) {
      console.error("❌ Screen sharing failed:", error.message);
      return { success: false, error: error.message };
    }
  });

  if (!result.success) {
    console.error("Failed to start screen sharing:", result.error);
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active:", result);

  // Wait for screen sharing to stabilize
  await new Promise((resolve) => setTimeout(resolve, 2000));

  console.log("3️⃣ Testing safe navigation...");

  // Method 1: Stop streams before navigation
  console.log("🛑 Stopping streams before navigation...");
  await page.evaluate(() => {
    window.streamManager.stopAllStreams();
  });

  // Wait for cleanup
  await new Promise((resolve) => setTimeout(resolve, 1000));

  console.log("🔗 Navigating safely...");
  try {
    await page.goto("https://www.google.com/search?q=test", {
      waitUntil: "domcontentloaded",
      timeout: 10000,
    });
    console.log("✅ Navigation successful!");
  } catch (error) {
    console.error("❌ Navigation failed:", error.message);
  }

  // Test creating new streams after navigation
  console.log("4️⃣ Testing new stream after navigation...");

  const newStreamResult = await page.evaluate(async () => {
    try {
      const newStream = await navigator.mediaDevices.getDisplayMedia({
        video: { frameRate: 15 },
        preferCurrentTab: true,
      });

      console.log("✅ New stream created after navigation");
      return {
        success: true,
        streamId: newStream.id,
        streamsCount: window.streamManager.activeStreams.size,
      };
    } catch (error) {
      console.error("❌ New stream failed:", error.message);
      return { success: false, error: error.message };
    }
  });

  console.log("New stream result:", newStreamResult);

  // Final cleanup and status
  const finalStatus = await page.evaluate(() => {
    const status = window.streamManager.getStatus();
    console.log("📊 Final stream status:", status);
    return status;
  });

  console.log("📋 Final status:", finalStatus);

  console.log("\n🎉 Test completed successfully!");
  console.log("✅ Screen sharing works without crashes");
  console.log("✅ Navigation works safely");
  console.log("✅ New streams can be created after navigation");

  console.log("\n🔍 Browser will remain open for manual testing.");
  console.log("Try manually creating streams and navigating to test further.");

  // Keep browser open for manual testing
  // Uncomment to auto-close: await browser.close();
})();
