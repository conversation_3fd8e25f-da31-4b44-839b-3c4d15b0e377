/Users/<USER>/pup/node_modules/@puppeteer/browsers/lib/cjs/launch.js:325
                reject(new Error([
                       ^

Error: Failed to launch the browser process!
[49357:9338555:0603/010510.397139:ERROR:socket_posix.cc(147)] bind() failed: Address already in use (48)
[49357:9338555:0603/010510.397274:ERROR:socket_posix.cc(147)] bind() failed: Address already in use (48)
[49357:9338555:0603/010510.397295:ERROR:devtools_http_handler.cc(314)] Cannot start http server for devtools.
[49357:9338545:0603/010513.490014:ERROR:registration_request.cc(291)] Registration response error message: PHONE_REGISTRATION_ERROR
[49357:9338545:0603/010513.490131:ERROR:registration_request.cc(291)] Registration response error message: PHONE_REGISTRATION_ERROR
[49357:9338545:0603/010513.501776:ERROR:registration_request.cc(291)] Registration response error message: PHONE_REGISTRATION_ERROR


TROUBLESHOOTING: https://pptr.dev/troubleshooting

    at ChildProcess.onClose (/Users/<USER>/pup/node_modules/@puppeteer/browsers/lib/cjs/launch.js:325:24)
    at ChildProcess.emit (node:events:530:35)
    at ChildProcess._handle.onexit (node:internal/child_process:294:12)

Node.js v20.12.2
