const puppeteer = require("puppeteer");
const fs = require("fs");
const path = require("path");
const fetch = require("node-fetch");

/**
 * CDP Frame Manager - Mimics Puppeteer's FrameManager behavior
 * Handles frame discovery, script injection, and lifecycle management
 */
class CDPFrameManager {
  constructor(rootSession) {
    this.rootSession = rootSession;
    this.frames = new Map(); // frameId -> frame info
    this.sessions = new Map(); // sessionId -> session
    this.scriptsToEvaluate = new Map(); // scriptId -> script info

    this.setupEventListeners(rootSession);
  }

  setupEventListeners(session) {
    // Listen for frame events
    session.on("Page.frameAttached", async (event) => {
      this.onFrameAttached(event.frameId, event.parentFrameId);
    });

    session.on("Page.frameNavigated", async (event) => {
      this.onFrameNavigated(event.frame);
    });

    session.on("Page.frameDetached", async (event) => {
      this.onFrameDetached(event.frameId, event.reason);
    });

    // Listen for target events (OOP iframes)
    session.on("Target.attachedToTarget", async (event) => {
      await this.onTargetAttached(event);
    });
  }

  async initialize() {
    console.log("🔧 Initializing CDP Frame Manager...");

    // Enable required domains
    await this.rootSession.send("Page.enable");
    await this.rootSession.send("Runtime.enable");
    await this.rootSession.send("Page.setLifecycleEventsEnabled", {
      enabled: true,
    });

    // Setup auto-attach for OOP iframes
    await this.rootSession.send("Target.setAutoAttach", {
      autoAttach: true,
      waitForDebuggerOnStart: false,
      flatten: true,
    });

    console.log("✓ CDP Frame Manager initialized");
  }

  onFrameAttached(frameId, parentFrameId, session = this.rootSession) {
    if (this.frames.has(frameId)) {
      console.log(`📎 Frame ${frameId} already exists, updating session`);
      const frame = this.frames.get(frameId);
      frame.session = session;
      return;
    }

    const frame = {
      id: frameId,
      parentId: parentFrameId,
      session: session,
      url: null,
      isMainFrame: !parentFrameId,
      scriptsInjected: new Set(),
    };

    this.frames.set(frameId, frame);
    console.log(
      `📎 Frame attached: ${frameId} (parent: ${parentFrameId || "none"})`
    );

    // Apply existing scripts to this frame
    this.applyScriptsToFrame(frame);
  }

  onFrameNavigated(framePayload, session = this.rootSession) {
    const frameId = framePayload.id;
    const isMainFrame = !framePayload.parentId;
    console.log(`🧭 Frame navigated: ${frameId} -> ${framePayload.url}`);
    let frame = this.frames.get(frameId);
    if (!frame) {
      frame = {
        id: frameId,
        parentId: framePayload.parentId,
        session: session,
        url: framePayload.url,
        isMainFrame: isMainFrame,
        scriptsInjected: new Set(),
      };
      this.frames.set(frameId, frame);
    } else {
      frame.url = framePayload.url;
      frame.session = session;
      // Clear injected scripts on navigation
      frame.scriptsInjected.clear();
    }

    console.log(`🧭 Frame navigated: ${frameId} -> ${framePayload.url}`);

    // Apply existing scripts to this frame after navigation
    this.applyScriptsToFrame(frame);
  }

  onFrameDetached(frameId, reason) {
    const frame = this.frames.get(frameId);
    if (!frame) {
      return;
    }

    switch (reason) {
      case "remove":
        // Only remove the frame if the reason for the detached event is
        // an actual removal of the frame.
        console.log(`📎 Frame detached (removed): ${frameId}`);
        this.frames.delete(frameId);
        break;
      case "swap":
        // For frames that become OOP iframes, the reason would be 'swap'.
        // Don't remove the frame, just emit a swapped event.
        console.log(`🔄 Frame swapped: ${frameId}`);
        this.onFrameSwapped(frame);
        break;
      default:
        // Fallback for unknown reasons - treat as removal
        console.log(`📎 Frame detached (${reason || "unknown"}): ${frameId}`);
        this.frames.delete(frameId);
        break;
    }
  }

  onFrameSwapped(frame) {
    console.log(`🔄 Frame swapped: ${frame.id} - maintaining frame identity`);
    // Frame is swapped but not removed - this typically happens when
    // a frame becomes an OOP iframe or vice versa.
    // We keep the frame in our collection but it may get a new session later.

    // Clear injected scripts since the frame context is changing
    frame.scriptsInjected.clear();

    // The frame will be updated with a new session when Target.attachedToTarget
    // is fired or when it's reattached with a new session
  }

  async onTargetAttached({ sessionId, targetInfo }) {
    console.log("🎯 New target attached:", {
      sessionId,
      type: targetInfo.type,
      url: targetInfo.url,
      targetId: targetInfo.targetId,
    });

    if (targetInfo.type !== "iframe") {
      return;
    }

    try {
      const session = this.rootSession.connection().session(sessionId);
      this.sessions.set(sessionId, session);

      // Get the frame that this target represents
      const frame = this.frames.get(targetInfo.targetId);
      if (frame) {
        // Update frame with new session (like frame.updateClient in Puppeteer)
        frame.session = session;
        console.log(
          `🔄 Updated frame ${targetInfo.targetId} with new session ${sessionId}`
        );
      }

      // Initialize the session (like void this.initialize(target._session()!, frame))
      await this.initializeSession(session, frame);
    } catch (error) {
      console.warn(
        `⚠️ Failed to setup iframe session ${sessionId}:`,
        error.message
      );
    }
  }

  /**
   * Initialize a session (enable domains and apply scripts)
   * This mirrors the initialize method in Puppeteer's FrameManager
   */
  async initializeSession(session, frame) {
    console.log("🔧 Initializing session...");

    try {
      // Enable required domains for the iframe session
      await session.send("Page.enable");
      await session.send("Runtime.enable");
      await session.send("Page.setLifecycleEventsEnabled", { enabled: true });

      console.log("✓ Session domains enabled");

      // Apply existing scripts to the frame if it exists
      if (frame) {
        await this.applyScriptsToFrame(frame);
        console.log(`✓ Applied scripts to frame ${frame.id}`);
      }
    } catch (error) {
      console.warn("⚠️ Failed to initialize session:", error.message);
    }
  }

  /**
   * Mimics Puppeteer's evaluateOnNewDocument method
   * This is the key method that implements the FrameManager approach
   */
  async evaluateOnNewDocument(source) {
    console.log("📝 Registering script for evaluation on new document...");

    // Step 1: Register script with main frame (like Puppeteer does)
    const { identifier } = await this.rootSession.send(
      "Page.addScriptToEvaluateOnNewDocument",
      {
        source,
        worldName: "testing-world",
      }
    );

    // Step 2: Store script info for future frames
    const scriptInfo = {
      identifier,
      source,
      worldName: "testing-world",
    };
    this.scriptsToEvaluate.set(identifier, scriptInfo);

    console.log(`✓ Registered script with identifier: ${identifier}`);

    // Step 3: Apply to ALL existing frames (this is the key insight from FrameManager)
    await this.applyScriptToAllFrames(scriptInfo);

    return { identifier };
  }

  /**
   * Apply a script to all currently known frames
   * This mimics the Promise.all(this.frames().map(...)) pattern from FrameManager
   */
  async applyScriptToAllFrames(scriptInfo) {
    const frameArray = Array.from(this.frames.values());
    console.log(
      `🔄 Applying script to ${frameArray.length} existing frames...`
    );

    await Promise.all(
      frameArray.map(async (frame) => {
        return await this.applyScriptToFrame(frame, scriptInfo);
      })
    );

    console.log(`✓ Script applied to all ${frameArray.length} frames`);
  }

  /**
   * Apply scripts to a specific frame
   */
  async applyScriptsToFrame(frame) {
    const scripts = Array.from(this.scriptsToEvaluate.values());
    if (scripts.length === 0) return;

    console.log(
      `📋 Applying ${scripts.length} scripts to frame ${frame.id}...`
    );

    await Promise.all(
      scripts.map(async (scriptInfo) => {
        return await this.applyScriptToFrame(frame, scriptInfo);
      })
    );
  }

  /**
   * Apply a single script to a single frame
   */
  async applyScriptToFrame(frame, scriptInfo) {
    if (frame.scriptsInjected.has(scriptInfo.identifier)) {
      return;
    }

    try {
      if (!frame.mainFrame) {
        await frame.session.send("Page.addScriptToEvaluateOnNewDocument", {
          source: scriptInfo.source,
          worldName: scriptInfo.worldName,
        });
        await frame.session.send("Runtime.evaluate", {
          expression: scriptInfo.source,
          contextId: undefined, // Use default context
          includeCommandLineAPI: false,
          silent: true,
          returnByValue: false,
        });
        console.log(
          `✓ Injected script ${scriptInfo.identifier} into current document of frame ${frame.id}`
        );
      }

      frame.scriptsInjected.add(scriptInfo.identifier);
      console.log(
        `✓ Applied script ${scriptInfo.identifier} to frame ${frame.id} (future + current)`
      );
    } catch (error) {
      console.warn(
        `⚠️ Failed to apply script to frame ${frame.id}:`,
        error.message
      );
    }
  }

  /**
   * Get all frames (mimics FrameManager.frames() method)
   */
  getAllFrames() {
    return Array.from(this.frames.values());
  }

  /**
   * Get main frame
   */
  getMainFrame() {
    for (const frame of this.frames.values()) {
      if (frame.isMainFrame) {
        return frame;
      }
    }
    return null;
  }
}

/**
 * Main execution function - Uses the CDP Frame Manager
 */
(async () => {
  console.log("🚀 Starting CDP Frame Manager Demo...");

  // Launch browser
  await puppeteer.launch({
    headless: false,
    args: ["--remote-debugging-port=9222"],
    defaultViewport: null,
  });

  await new Promise((res) => setTimeout(res, 1000)); // Wait for Chrome to start

  // Connect to browser
  const res = await fetch("http://localhost:9223/json/version");
  const { webSocketDebuggerUrl } = await res.json();

  const browser = await puppeteer.connect({
    browserWSEndpoint: webSocketDebuggerUrl,
  });
  const page = await browser.newPage();

  // Create CDP session and frame manager
  const rootSession = await page.createCDPSession();
  const frameManager = new CDPFrameManager(rootSession);

  // Initialize frame manager
  await frameManager.initialize();

  // Load the script to inject
  // const scriptSource = fs.readFileSync(
  //   path.join(__dirname, "input-focus-listener.js"),
  //   "utf8"
  // );

  const scriptSource = "console.log('hii from new page', document.URL);";

  // Use the frame manager to evaluate script on new document
  // This mimics Puppeteer's evaluateOnNewDocument behavior
  await frameManager.evaluateOnNewDocument(scriptSource);

  // Navigate to test page
  console.log("🌐 Navigating to test page...");

  const pageLoadPromise = new Promise((resolve) => {
    rootSession.once("Page.loadEventFired", resolve);
  });

  await rootSession.send("Page.navigate", {
    url: `https://2captcha.com/demo/mtcaptcha`,
  });

  await pageLoadPromise;
  console.log("✓ Page loaded successfully!");

  // Wait a bit for any delayed iframes to load
  await new Promise((res) => setTimeout(res, 3000));

  // Log final frame status
  const allFrames = frameManager.getAllFrames();
  console.log(`\n📊 Final Status:`);
  console.log(`   Total frames discovered: ${allFrames.length}`);

  allFrames.forEach((frame, index) => {
    console.log(`   Frame ${index + 1}: ${frame.id}`);
    console.log(`     URL: ${frame.url || "unknown"}`);
    console.log(`     Type: ${frame.isMainFrame ? "main" : "iframe"}`);
    console.log(`     Scripts injected: ${frame.scriptsInjected.size}`);
  });

  console.log("\n🎉 CDP Frame Manager demo completed!");
})().catch(console.error);
