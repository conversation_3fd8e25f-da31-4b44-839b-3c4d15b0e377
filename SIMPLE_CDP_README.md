# Simple CDP Browser Automation Script

This project demonstrates how to use the `simple-cdp` package to automate browser interactions using the Chrome DevTools Protocol (CDP).

## Files Created

- `simple-cdp-script.js` - Main automation script with multiple examples
- `start-chrome.sh` - Helper script to start Chrome with debugging enabled
- `SIMPLE_CDP_README.md` - This documentation file

## Prerequisites

1. **Chrome/Chromium Browser**: Make sure you have Chrome or Chromium installed
2. **Node.js**: Required to run the JavaScript script
3. **simple-cdp package**: Already installed via `npm install simple-cdp`

## Quick Start

### Step 1: Start Chrome with Remote Debugging

```bash
# Option 1: Use the helper script
./start-chrome.sh

# Option 2: Start Chrome manually
google-chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check

# Option 3: On macOS with Chrome app
"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" --remote-debugging-port=9222 --no-first-run --no-default-browser-check
```

### Step 2: Run the Script

```bash
node simple-cdp-script.js
```

## What the Script Does

The script demonstrates five main examples:

### 1. Basic Example

- Creates a target for `https://example.com`
- Enables the Runtime domain
- Evaluates JavaScript expressions
- Extracts page information (title, URL, links)

### 2. Advanced Example with Auto-Attach

- Sets up auto-attachment to new targets
- Handles iframe and new tab creation
- Demonstrates session management
- Analyzes page elements and interactions

### 3. Page Navigation Example

- Shows how to navigate between pages
- Demonstrates content extraction
- Shows how to go back to previous pages

### 4. Input Dispatching Example

- Demonstrates mouse clicks on form elements
- Shows keyboard input and text typing
- Handles Tab key navigation between fields
- Shows special key presses (Escape, Enter)
- Demonstrates mouse wheel scrolling
- Extracts and verifies form state

### 5. Advanced Input Dispatching Example

- Keyboard shortcuts (Ctrl+A, Shift+Tab)
- Mouse drag operations
- Right-click context menus
- Double-click events
- Function keys (F12)
- Arrow key navigation
- Complex modifier key combinations

## Key Features Demonstrated

- **Target Creation**: Creating new browser targets/tabs
- **JavaScript Evaluation**: Running JavaScript in the browser context
- **DOM Interaction**: Querying and analyzing page elements
- **Event Handling**: Listening for target attachment events
- **Session Management**: Working with multiple browser sessions
- **Page Navigation**: Programmatic navigation between pages
- **Mouse Input**: Click, drag, right-click, double-click, wheel scroll
- **Keyboard Input**: Text typing, special keys, modifier combinations
- **Form Interaction**: Filling forms, navigating between fields
- **Advanced Input**: Keyboard shortcuts, complex mouse operations

## Script Output

The script provides detailed console output showing:

- Target creation and attachment
- JavaScript evaluation results
- Page information extraction
- Element analysis
- Navigation events

## Troubleshooting

### Chrome Not Starting

- Make sure Chrome is installed
- Check the paths in `start-chrome.sh`
- Try running Chrome manually with the debugging flags

### Connection Errors

- Ensure Chrome is running with `--remote-debugging-port=9222`
- Check that port 9222 is not blocked by firewall
- Verify Chrome is accessible at `http://localhost:9222`

### Permission Errors

- Make sure `start-chrome.sh` is executable: `chmod +x start-chrome.sh`
- Check file permissions in the project directory

## Extending the Script

You can extend the script by:

1. **Adding more CDP domains**: Enable Page, Network, Security, etc.
2. **Implementing form interactions**: Fill forms, click buttons
3. **Screenshot capture**: Use Page.captureScreenshot
4. **Network monitoring**: Track requests and responses
5. **Performance analysis**: Monitor page load times and resources

## Example Extensions

```javascript
// Take a screenshot
await cdpInstance.Page.captureScreenshot({ format: "png" });

// Monitor network requests
await cdpInstance.Network.enable();
cdpInstance.Network.addEventListener("requestWillBeSent", (event) => {
  console.log("Request:", event.params.request.url);
});

// Fill a form field using JavaScript
await cdpInstance.Runtime.evaluate({
  expression: `document.querySelector('#email').value = '<EMAIL>'`,
});

// Fill a form field using Input dispatching (more realistic)
await cdpInstance.Input.dispatchMouseEvent({
  type: "mousePressed",
  x: 100,
  y: 200,
  button: "left",
  clickCount: 1,
});
await cdpInstance.Input.dispatchMouseEvent({
  type: "mouseReleased",
  x: 100,
  y: 200,
  button: "left",
  clickCount: 1,
});

// Type text character by character
for (const char of "<EMAIL>") {
  await cdpInstance.Input.dispatchKeyEvent({
    type: "keyDown",
    text: char,
  });
  await cdpInstance.Input.dispatchKeyEvent({
    type: "keyUp",
    text: char,
  });
}
```

## Input Dispatching Reference

### Mouse Events

- `mousePressed` / `mouseReleased` - Click events
- `mouseMoved` - Mouse movement and drag operations
- `mouseWheel` - Scroll wheel events

### Keyboard Events

- `keyDown` / `keyUp` - Key press events
- `text` - Character to type
- `key` - Key name (e.g., 'Enter', 'Tab', 'Escape')
- `code` - Physical key code (e.g., 'KeyA', 'ArrowUp')
- `modifiers` - Modifier flags (1=Alt, 2=Ctrl, 4=Meta, 8=Shift)

### Common Modifier Values

- `1` - Alt key
- `2` - Ctrl key
- `4` - Meta/Cmd key
- `8` - Shift key
- Combine with bitwise OR: `2 | 8` = Ctrl+Shift

## Resources

- [simple-cdp GitHub Repository](https://github.com/gildas-lormeau/simple-cdp)
- [Chrome DevTools Protocol Documentation](https://chromedevtools.github.io/devtools-protocol/)
- [CDP Domains Reference](https://chromedevtools.github.io/devtools-protocol/tot/)

## Notes

- The script uses CommonJS modules (`require`) as specified in package.json
- Chrome will create a temporary profile in `/tmp/chrome-debug-profile`
- The script handles graceful shutdown with Ctrl+C
- All examples include error handling and detailed logging
