const puppeteer = require("puppeteer");
const path = require("path");

async function testCDPApproach() {
  console.log("\n🔧 === TESTING CDP APPROACH ===");

  const browser = await puppeteer.launch({
    headless: false,
    args: ["--remote-debugging-port=9222"],
    defaultViewport: null,
  });

  await new Promise((res) => setTimeout(res, 1000));

  const res = await fetch("http://localhost:9222/json/version");
  const { webSocketDebuggerUrl } = await res.json();

  const cdpBrowser = await puppeteer.connect({
    browserWSEndpoint: webSocketDebuggerUrl,
  });
  const page = await cdpBrowser.newPage();

  const rootSession = await page.createCDPSession();

  // Simple test script to log when it's injected
  const testScript = `
    console.log("🚀 [CDP] Script injected in:", window.location.href);
    console.log("🚀 [CDP] Is iframe:", window !== window.top);
  `;

  await rootSession.send("Page.enable");
  await rootSession.send("Runtime.enable");

  // Inject into main page
  await rootSession.send("Page.addScriptToEvaluateOnNewDocument", {
    source: testScript,
  });
  console.log("✓ [CDP] Script registered for main page");

  // Setup auto-attach for iframes
  await rootSession.send("Target.setAutoAttach", {
    autoAttach: true,
    waitForDebuggerOnStart: false,
    flatten: true,
  });

  let iframeCount = 0;
  rootSession.on(
    "Target.attachedToTarget",
    async ({ sessionId, targetInfo }) => {
      if (targetInfo.type === "iframe") {
        iframeCount++;
        console.log(`🎯 [CDP] Iframe ${iframeCount} detected:`, {
          url: targetInfo.url || "empty",
          title: targetInfo.title || "no title",
        });

        try {
          const session = rootSession.connection().session(sessionId);
          await session.send("Page.enable");
          await session.send("Runtime.enable");
          await session.send("Page.addScriptToEvaluateOnNewDocument", {
            source: testScript,
          });
          console.log(`✓ [CDP] Script injected into iframe ${iframeCount}`);
        } catch (err) {
          console.warn(
            `⚠️ [CDP] Failed to inject into iframe ${iframeCount}:`,
            err.message
          );
        }
      }
    }
  );

  // Navigate and wait
  await rootSession.send("Page.navigate", {
    url: `file://${path.join(__dirname, "test-iframes-clean.html")}`,
  });

  await new Promise((resolve) => {
    rootSession.once("Page.loadEventFired", resolve);
  });

  console.log("⏳ [CDP] Waiting 5 seconds for delayed iframes...");
  await new Promise((res) => setTimeout(res, 5000));

  console.log(`📊 [CDP] Total iframes detected: ${iframeCount}`);

  await cdpBrowser.close();
  return iframeCount;
}

async function testPuppeteerApproach() {
  console.log("\n🎭 === TESTING PUPPETEER APPROACH ===");

  const browser = await puppeteer.launch({
    headless: false,
    args: ["--remote-debugging-port=9223"],
    defaultViewport: null,
  });

  const page = await browser.newPage();

  // Simple test script to log when it's injected
  const testScript = `
    console.log("🚀 [PUPPETEER] Script injected in:", window.location.href);
    console.log("🚀 [PUPPETEER] Is iframe:", window !== window.top);
  `;

  await page.evaluateOnNewDocument(testScript);
  console.log("✓ [PUPPETEER] Script registered with evaluateOnNewDocument");

  await page.goto(`file://${path.join(__dirname, "test-iframes-clean.html")}`, {
    waitUntil: "domcontentloaded",
  });

  console.log("⏳ [PUPPETEER] Waiting 5 seconds for delayed iframes...");
  await new Promise((res) => setTimeout(res, 5000));

  const frames = page.frames();
  console.log(`📊 [PUPPETEER] Total frames detected: ${frames.length}`);

  frames.forEach((frame, index) => {
    console.log(`Frame ${index}:`, {
      url: frame.url(),
      isDetached: frame.isDetached(),
    });
  });

  await browser.close();
  return frames.length - 1; // Subtract main frame
}

async function runComparison() {
  try {
    const cdpIframes = await testCDPApproach();
    await new Promise((res) => setTimeout(res, 2000)); // Wait between tests
    const puppeteerIframes = await testPuppeteerApproach();

    console.log("\n📈 === COMPARISON RESULTS ===");
    console.log(`CDP detected iframes: ${cdpIframes}`);
    console.log(`Puppeteer detected iframes: ${puppeteerIframes}`);
    console.log(
      `Difference: ${puppeteerIframes - cdpIframes} iframes missed by CDP`
    );

    if (cdpIframes < puppeteerIframes) {
      console.log("❌ CDP approach is missing iframe detection");
    } else {
      console.log("✅ Both approaches detected the same number of iframes");
    }
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

runComparison();
