const puppeteer = require("puppeteer");

(async () => {
  console.log("🚀 Starting screen sharing crash test...");
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: [
      "--remote-debugging-port=9222",
      "--remote-allow-origins=*",
      "--auto-accept-this-tab-capture",
      "--use-fake-ui-for-media-stream", // Auto-accept media permissions
      "--use-fake-device-for-media-stream",
      "--allow-running-insecure-content",
      "--disable-web-security",
      "--disable-features=VizDisplayCompositor"
    ],
  });

  const page = await browser.newPage();

  // Grant permissions for screen capture
  const context = browser.defaultBrowserContext();
  await context.overridePermissions('https://google.com', ['camera', 'microphone', 'display-capture']);

  console.log("📱 Navigating to Google...");
  await page.goto("https://google.com", { waitUntil: "domcontentloaded" });

  console.log("🎥 Starting screen sharing...");
  
  // Inject the screen sharing script
  const screenSharingResult = await page.evaluate(async () => {
    try {
      console.log("Requesting display media...");
      
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.innerWidth,
          height: window.innerHeight,
        },
        preferCurrentTab: true,
      });

      console.log("✅ Screen sharing started successfully!");
      console.log("Stream details:", {
        id: screenStream.id,
        active: screenStream.active,
        videoTracks: screenStream.getVideoTracks().length,
        audioTracks: screenStream.getAudioTracks().length
      });

      // Store the stream globally so we can access it later
      window.screenStream = screenStream;

      // Add event listeners to detect when stream ends
      screenStream.addEventListener('inactive', () => {
        console.log("🔴 Screen stream became inactive");
      });

      screenStream.getVideoTracks().forEach(track => {
        track.addEventListener('ended', () => {
          console.log("🔴 Video track ended");
        });
      });

      return {
        success: true,
        streamId: screenStream.id,
        active: screenStream.active
      };
    } catch (error) {
      console.error("❌ Failed to start screen sharing:", error);
      return {
        success: false,
        error: error.message
      };
    }
  });

  if (!screenSharingResult.success) {
    console.error("❌ Screen sharing failed:", screenSharingResult.error);
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active:", screenSharingResult);

  // Wait a moment for screen sharing to stabilize
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log("🔗 Looking for links to click...");
  
  // Find and click on a link to trigger navigation
  const linkClicked = await page.evaluate(() => {
    // Look for various types of links on Google's homepage
    const selectors = [
      'a[href*="gmail"]',
      'a[href*="images"]', 
      'a[href*="maps"]',
      'a[href*="youtube"]',
      'a[href*="news"]',
      'a[href*="shopping"]',
      'a:not([href="#"]):not([href=""])',  // Any link that's not empty or just hash
    ];

    for (const selector of selectors) {
      const links = document.querySelectorAll(selector);
      if (links.length > 0) {
        const link = links[0];
        console.log(`Clicking on link: ${link.href} (text: "${link.textContent.trim()}")`);
        link.click();
        return {
          success: true,
          href: link.href,
          text: link.textContent.trim()
        };
      }
    }

    return { success: false, message: "No suitable links found" };
  });

  if (linkClicked.success) {
    console.log(`🖱️ Clicked on link: ${linkClicked.href} (${linkClicked.text})`);
  } else {
    console.log("⚠️ No links found, trying to navigate manually...");
    await page.goto("https://www.google.com/search?q=test", { waitUntil: "domcontentloaded" });
  }

  // Monitor for crashes or errors
  page.on('error', (error) => {
    console.error("💥 Page error detected:", error.message);
  });

  page.on('pageerror', (error) => {
    console.error("💥 Page script error:", error.message);
  });

  // Check if screen sharing is still active after navigation
  await new Promise(resolve => setTimeout(resolve, 3000));

  const streamStatus = await page.evaluate(() => {
    if (window.screenStream) {
      return {
        exists: true,
        active: window.screenStream.active,
        videoTracks: window.screenStream.getVideoTracks().map(track => ({
          enabled: track.enabled,
          readyState: track.readyState
        }))
      };
    }
    return { exists: false };
  });

  console.log("📊 Stream status after navigation:", streamStatus);

  if (!streamStatus.exists || !streamStatus.active) {
    console.log("🔴 Screen sharing appears to have stopped or crashed!");
  } else {
    console.log("✅ Screen sharing still active after navigation");
  }

  console.log("🔍 Test completed. Browser will remain open for manual inspection.");
  console.log("Check the browser console for any additional error messages.");
  
  // Keep browser open for manual inspection
  // Uncomment the next line to auto-close after 30 seconds
  // setTimeout(() => browser.close(), 30000);
})();
