const puppeteer = require("puppeteer");
const HP = require("@hyperbrowser/sdk");

(async () => {
  console.log("🚀 Starting detailed screen sharing crash test...");

  // const browser = await puppeteer.launch({
  //   headless: false,
  //   defaultViewport: null,
  //   args: [
  //     "--remote-debugging-port=9222",
  //     "--remote-allow-origins=*",
  //     "--auto-accept-this-tab-capture",
  //     "--allow-running-insecure-content",
  //     "--disable-web-security",
  //   ],
  // });

  const hyperBrowser = new HP.Hyperbrowser({
    apiKey: "hb_28aac10409666bbccf859a9b8804",
    timeout: 60000,
  });
  const session = await hyperBrowser.sessions.create({
    browserArgs: ["--auto-accept-this-tab-capture"],
    device: ["desktop"],
  });
  const browser = await puppeteer.connect({
    browserWSEndpoint: session.wsEndpoint,
  });
  const page = (await browser.pages())[0];

  // Set up comprehensive error monitoring
  page.on("error", (error) => {
    console.error("💥 PAGE ERROR:", error.message);
    console.error("Stack:", error.stack);
  });

  page.on("pageerror", (error) => {
    console.error("💥 PAGE SCRIPT ERROR:", error.message);
    console.error("Stack:", error.stack);
  });

  page.on("console", (msg) => {
    const type = msg.type();
    const text = msg.text();
    if (type === "error") {
      console.error(`🔴 CONSOLE ERROR: ${text}`);
    } else if (type === "warning") {
      console.warn(`🟡 CONSOLE WARNING: ${text}`);
    } else {
      console.log(`📝 CONSOLE ${type.toUpperCase()}: ${text}`);
    }
  });

  page.on("response", (response) => {
    if (!response.ok()) {
      console.warn(`⚠️ HTTP ERROR: ${response.status()} ${response.url()}`);
    }
  });

  // Inject monitoring script that will persist across navigations
  await page.evaluateOnNewDocument(() => {
    window.crashTestMonitor = {
      streamActive: false,
      navigationCount: 0,
      errors: [],

      logError: function (error, context) {
        const errorInfo = {
          timestamp: new Date().toISOString(),
          context: context,
          message: error.message || error,
          stack: error.stack,
        };
        this.errors.push(errorInfo);
        console.error(`💥 CRASH MONITOR [${context}]:`, errorInfo);
      },

      checkStreamHealth: function () {
        if (window.screenStream) {
          const wasActive = this.streamActive;
          this.streamActive = window.screenStream.active;

          if (wasActive && !this.streamActive) {
            this.logError(
              new Error("Screen stream became inactive"),
              "STREAM_HEALTH"
            );
          }

          return {
            active: this.streamActive,
            tracks: window.screenStream.getVideoTracks().map((track) => ({
              enabled: track.enabled,
              readyState: track.readyState,
              label: track.label,
            })),
          };
        }
        return { active: false, tracks: [] };
      },
    };

    // Monitor navigation events
    window.addEventListener("beforeunload", () => {
      window.crashTestMonitor.navigationCount++;
      console.log(
        `🔄 NAVIGATION ${window.crashTestMonitor.navigationCount}: beforeunload`
      );

      if (window.screenStream) {
        console.log(
          "📊 Stream status before navigation:",
          window.crashTestMonitor.checkStreamHealth()
        );
      }
    });

    // Monitor for unhandled errors
    window.addEventListener("error", (event) => {
      window.crashTestMonitor.logError(event.error, "UNHANDLED_ERROR");
    });

    window.addEventListener("unhandledrejection", (event) => {
      window.crashTestMonitor.logError(event.reason, "UNHANDLED_REJECTION");
    });
  });

  console.log("📱 Navigating to Google...");
  await page.goto("https://google.com", { waitUntil: "domcontentloaded" });

  console.log("🎥 Starting screen sharing...");

  const screenSharingResult = await page.evaluate(async () => {
    try {
      console.log("Requesting display media...");

      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.innerWidth,
          height: window.innerHeight,
        },
        preferCurrentTab: true,
      });

      console.log("✅ Screen sharing started successfully!");

      // Store globally and set up monitoring
      window.screenStream = screenStream;
      window.crashTestMonitor.streamActive = true;

      // Enhanced event listeners
      screenStream.addEventListener("inactive", () => {
        console.log("🔴 Screen stream became inactive");
        window.crashTestMonitor.logError(
          new Error("Stream inactive event"),
          "STREAM_EVENT"
        );
      });

      screenStream.getVideoTracks().forEach((track, index) => {
        track.addEventListener("ended", () => {
          console.log(`🔴 Video track ${index} ended`);
          window.crashTestMonitor.logError(
            new Error(`Video track ${index} ended`),
            "TRACK_EVENT"
          );
        });

        track.addEventListener("mute", () => {
          console.log(`🔇 Video track ${index} muted`);
        });
      });
      screenStream.getTracks().forEach((track) => track.stop());

      return {
        success: true,
        streamId: screenStream.id,
        active: screenStream.active,
        trackCount: screenStream.getVideoTracks().length,
      };
    } catch (error) {
      window.crashTestMonitor.logError(error, "SCREEN_SHARING_INIT");
      return {
        success: false,
        error: error.message,
      };
    }
  });

  if (!screenSharingResult.success) {
    console.error("❌ Screen sharing failed:", screenSharingResult.error);
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active:", screenSharingResult);
  await new Promise((resolve) => setTimeout(resolve, 8000));

  console.log("🔗 Triggering navigation to reproduce crash...");

  // Try multiple navigation scenarios
  const navigationTests = [
    { name: "Google Images", url: "https://images.google.com" },
    { name: "Google Search", url: "https://www.google.com/search?q=test" },
    { name: "Google Maps", url: "https://maps.google.com" },
  ];

  for (const test of navigationTests) {
    console.log(`\n🧪 Testing navigation to ${test.name}...`);

    try {
      await page.goto(test.url, {
        waitUntil: "domcontentloaded",
        timeout: 10000,
      });

      // Check stream status after navigation
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const status = await page.evaluate(() => {
        return window.crashTestMonitor
          ? window.crashTestMonitor.checkStreamHealth()
          : null;
      });

      console.log(`📊 Stream status after ${test.name}:`, status);

      const errors = await page.evaluate(() => {
        return window.crashTestMonitor ? window.crashTestMonitor.errors : [];
      });

      if (errors.length > 0) {
        console.log(`🚨 Errors detected during ${test.name}:`, errors);
      }
    } catch (error) {
      console.error(`❌ Navigation to ${test.name} failed:`, error.message);
    }

    // Wait between tests
    await new Promise((resolve) => setTimeout(resolve, 3000));
  }

  console.log("\n📋 Final crash test report:");
  const finalReport = await page.evaluate(() => {
    if (!window.crashTestMonitor) return null;

    return {
      navigationCount: window.crashTestMonitor.navigationCount,
      totalErrors: window.crashTestMonitor.errors.length,
      errors: window.crashTestMonitor.errors,
      finalStreamStatus: window.crashTestMonitor.checkStreamHealth(),
    };
  });

  console.log(finalReport);

  console.log(
    "\n🔍 Test completed. Browser will remain open for manual inspection."
  );
  console.log(
    "Check the browser console and DevTools for additional information."
  );
})();
