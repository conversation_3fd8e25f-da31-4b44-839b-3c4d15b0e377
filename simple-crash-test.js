import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";
(async () => {
  let browser;
  const useLocalBrowser = false;
  const launchNew = true;
  if (useLocalBrowser) {
    if (launchNew) {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [
          "--remote-debugging-port=9222",
          "--remote-allow-origins=*",
          "--no-first-run",
          "--auto-accept-this-tab-capture",
          "--no-default-browser-check",
        ],
      });
    } else {
      browser = await puppeteer.connect({
        browserWSEndpoint:
          "ws://localhost:9222/devtools/browser/0cbce5f8-1e35-407a-8703-d1043031d408",
      });
    }
  } else {
    const hyperBrowser = new Hyperbrowser({
      apiKey: "hb_28aac10409666bbccf859a9b8804",
      timeout: 60000,
    });

    // Create session with screen capture args
    console.log("🔧 Creating Hyperbrowser session...");
    const session = await hyperBrowser.sessions.create({
      browserArgs: ["--auto-accept-this-tab-capture"],
      device: ["desktop"],
    });
    browser = await puppeteer.connect({
      browserWSEndpoint:
        // "ws://localhost:9222/devtools/browser/fe1a03aa-cab2-4f7f-aaf6-9332d1c96cb4",
        session.wsEndpoint,
    });
  }

  const page = await browser.newPage();

  // Simple error monitoring - just console logs
  page.on("error", (error) => {
    console.error("💥 PAGE CRASH:", error.message);
  });

  page.on("pageerror", (error) => {
    console.error("💥 SCRIPT ERROR:", error.message);
  });

  // Log browser console messages
  page.on("console", (msg) => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️ BROWSER [${type.toUpperCase()}]: ${text}`);
  });

  console.log("1️⃣ Navigating to Google...");
  await page.goto("https://github.com/password_reset", {
    waitUntil: "domcontentloaded",
  });

  console.log("2️⃣ Starting screen sharing... in 4000ms");
  await new Promise((resolve) => setTimeout(resolve, 4000));

  const result = await page.evaluate(async () => {
    try {
      console.log("🎥 Requesting screen sharing...");
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.width,
          height: window.height,
        },
        preferCurrentTab: true,
      });
      // Store globally for monitoring
      window.screenStream = screenStream;

      // Log stream events
      screenStream.addEventListener("inactive", () => {
        console.log("🔴 Stream became inactive");
      });

      screenStream.getVideoTracks().forEach((track, i) => {
        track.addEventListener("ended", () => {
          console.log(`🔴 Video track ${i} ended`);
        });
      });

      console.log("✅ Screen sharing started successfully");
      return {
        success: true,
        streamId: screenStream.id,
        active: screenStream.active,
        tracks: screenStream.getVideoTracks().length,
      };
    } catch (error) {
      console.error("❌ Screen sharing failed:", error.message);
      return { success: false, error: error.message };
    }
  });

  if (!result.success) {
    console.error("Failed to start screen sharing:", result.error);
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active:", result);

  // Wait for screen sharing to stabilize
  await new Promise((resolve) => setTimeout(resolve, 2000));

  console.log("🛑 Stopping screen stream before navigation...");
  let streamStopped;
  try {
    streamStopped = await page.evaluate(() => {
      if (window.screenStream) {
        console.log("🛑 Stopping screen stream before navigation...");
        window.screenStream.getTracks().forEach((track) => {
          console.log(`🛑 trying to stop`);
          // track.stop();
          console.log(`🛑 Stopped track: ${track.kind} (${track.readyState})`);
        });
        const wasActive = window.screenStream.active;
        window.screenStream = null;
        return { stopped: true, wasActive };
      }
      return { stopped: false, wasActive: false };
    });

    if (streamStopped.stopped) {
      console.log(
        `✅ Stream stopped successfully (was active: ${streamStopped.wasActive})`
      );
    } else {
      console.log("⚠️ No active stream to stop");
    }
  } catch (error) {
    console.error("💥 CRASH DETECTED: Stream stopping caused target to close!");
    console.error("Error details:", error.message);
    console.log("🔍 This confirms the screen sharing crash bug!");

    // Try to check if browser is still alive
    try {
      const pages = await browser.pages();
      console.log(`📊 Browser status: ${pages.length} pages remaining`);
    } catch (browserError) {
      console.error("💥 Browser completely crashed:", browserError.message);
    }

    console.log("🏁 Test completed - crash successfully reproduced!");
    await browser.close();
    return;
  }

  // Wait a moment after stopping
  await new Promise((resolve) => setTimeout(resolve, 1000));

  console.log("3️⃣ Clicking link to trigger navigation...");

  const linkClicked = await page.evaluate(() => {
    console.log("🔍 Looking for header-logo link to click...");

    // Find the specific header-logo link
    const headerLogoLink = document.querySelector("a.header-logo");
    console.log(`Header logo link found:`, headerLogoLink ? "Yes" : "No");

    if (headerLogoLink) {
      console.log(
        `Clicking header logo: ${
          headerLogoLink.href
        } (${headerLogoLink.textContent.trim()})`
      );
      headerLogoLink.click();
      return {
        success: true,
        href: headerLogoLink.href,
        text: headerLogoLink.textContent.trim(),
      };
    }

    // Fallback to any clickable link if header-logo not found
    console.log("Header logo not found, looking for any clickable link...");
    const links = document.querySelectorAll(
      'a[href]:not([href="#"]):not([href=""])'
    );
    console.log(`Found ${links.length} fallback links`);

    if (links.length > 0) {
      const link = links[0];
      console.log(
        `Clicking fallback: ${link.href} (${link.textContent.trim()})`
      );
      link.click();
      return { success: true, href: link.href, text: link.textContent.trim() };
    }

    return { success: false };
  });

  if (linkClicked.success) {
    console.log(`🖱️ Clicked: ${linkClicked.href}`);
  } else {
    console.log("⚠️ No links found, navigating manually...");
    await page.goto("https://www.google.com/search?q=test");
  }

  console.log("⏳ Waiting to detect crash...");

  // Wait and monitor
  for (let i = 0; i < 10; i++) {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    try {
      const status = await page.evaluate(() => {
        if (window.screenStream) {
          return {
            active: window.screenStream.active,
            tracks: window.screenStream.getVideoTracks().map((t) => ({
              readyState: t.readyState,
              enabled: t.enabled,
            })),
          };
        }
        return null;
      });

      if (status) {
        console.log(`📊 Stream check ${i + 1}/10:`, status);
        if (!status.active) {
          console.log("🔴 Stream is no longer active!");
          break;
        }
      } else {
        console.log(
          `⚠️ Cannot access stream - possible crash at check ${i + 1}/10`
        );
        break;
      }
    } catch (error) {
      console.error(`💥 Error during check ${i + 1}/10:`, error.message);
      break;
    }
  }

  console.log(
    "🔍 Test completed. Check console output above for crash details."
  );
  console.log("Browser will remain open for manual inspection.");

  // Keep browser open
  // Uncomment to auto-close: await browser.close();
})();
