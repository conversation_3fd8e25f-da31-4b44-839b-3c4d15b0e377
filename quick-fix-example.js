const puppeteer = require("puppeteer");

/**
 * QUICK FIX EXAMPLE
 *
 * This shows how to modify your existing code to prevent crashes.
 * The key is to STOP all media streams before navigation.
 */

(async () => {
  console.log("🚀 Quick fix example - Your code with crash prevention");

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: [
      "--auto-accept-this-tab-capture",
      "--allow-running-insecure-content",
      "--disable-web-security",
    ],
  });

  const page = await browser.newPage();

  console.log("1️⃣ Navigate to google.com");
  await page.goto("https://google.com", { waitUntil: "domcontentloaded" });

  console.log("2️⃣ Start screen sharing (your original code)");

  // YOUR ORIGINAL CODE - with small addition to store stream globally
  const screenSharingResult = await page.evaluate(async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.width, // Note: this should probably be window.innerWidth
          height: window.height, // Note: this should probably be window.innerHeight
        },
        preferCurrentTab: true,
      });

      // 🔧 FIX: Store stream globally so we can stop it later
      window.currentScreenStream = screenStream;

      console.log("✅ Screen sharing started");
      return {
        success: true,
        streamId: screenStream.id,
        active: screenStream.active,
      };
    } catch (error) {
      console.error("❌ Screen sharing failed:", error);
      return { success: false, error: error.message };
    }
  });

  if (!screenSharingResult.success) {
    console.error("Screen sharing failed:", screenSharingResult.error);
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active:", screenSharingResult);

  // Simulate some time with active screen sharing
  await new Promise((resolve) => setTimeout(resolve, 3000));

  console.log("3️⃣ Click on any link to navigate (this would normally crash)");

  // 🔧 FIX: Stop the stream BEFORE navigation
  console.log("🛑 Stopping screen stream before navigation...");

  await page.evaluate(() => {
    if (window.currentScreenStream) {
      // Stop all tracks in the stream
      window.currentScreenStream.getTracks().forEach((track) => {
        track.stop();
        console.log(`⏹️ Stopped ${track.kind} track`);
      });

      // Clear the reference
      window.currentScreenStream = null;
      console.log("✅ Stream stopped and cleared");
    }
  });

  // Wait a moment for cleanup
  await new Promise((resolve) => setTimeout(resolve, 500));

  console.log("🔗 Now navigating safely...");

  try {
    // This navigation will now work without crashing
    await page.goto("https://www.google.com/search?q=test", {
      waitUntil: "domcontentloaded",
      timeout: 10000,
    });
    console.log("✅ Navigation successful - no crash!");
  } catch (error) {
    console.error("❌ Navigation failed:", error.message);
  }

  console.log("4️⃣ Test creating new stream after navigation");

  const newStreamResult = await page.evaluate(async () => {
    try {
      const newStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.innerWidth,
          height: window.innerHeight,
        },
        preferCurrentTab: true,
      });

      window.currentScreenStream = newStream;

      console.log("✅ New stream created successfully");
      return { success: true, streamId: newStream.id };
    } catch (error) {
      console.error("❌ New stream failed:", error);
      return { success: false, error: error.message };
    }
  });

  console.log("New stream result:", newStreamResult);

  console.log("\n🎉 SUCCESS! The crash has been fixed!");
  console.log("✅ Screen sharing works");
  console.log("✅ Navigation works without crashes");
  console.log("✅ New streams can be created after navigation");

  console.log("\n📋 KEY TAKEAWAYS:");
  console.log("1. Always stop media streams before navigation");
  console.log("2. Use stream.getTracks().forEach(track => track.stop())");
  console.log("3. Wait a moment after stopping before navigating");
  console.log("4. Clear stream references (set to null)");

  console.log("\n🔍 Browser will remain open for manual testing");

  // Keep browser open for manual testing
  // Uncomment to auto-close: await browser.close();
})();

/**
 * 📝 SUMMARY OF THE FIX:
 *
 * The crash happens because getDisplayMedia creates active media streams
 * that hold browser resources. When you navigate or close a page while
 * these streams are active, Puppeteer crashes.
 *
 * THE SOLUTION:
 * 1. Store the stream in a global variable: window.currentScreenStream = stream
 * 2. Before navigation, stop all tracks: stream.getTracks().forEach(track => track.stop())
 * 3. Clear the reference: window.currentScreenStream = null
 * 4. Wait a moment (500ms) before navigating
 *
 * This ensures all media resources are properly released before navigation,
 * preventing the crash.
 */
