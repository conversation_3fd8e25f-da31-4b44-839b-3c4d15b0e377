import puppeteer from "puppeteer";
import <PERSON> from "@hyperbrowser/sdk";

(async () => {
  console.log("🚀 Hyperbrowser screen sharing crash test - Console logging only");
  
  let hyperBrowser;
  let session;
  let browser;
  
  try {
    // Initialize Hyperbrowser
    console.log("🌐 Initializing Hyperbrowser...");
    hyperBrowser = new HP.Hyperbrowser({
      apiKey: "hb_28aac10409666bbccf859a9b8804",
      timeout: 60000,
    });
    
    // Create session with screen capture args
    console.log("🔧 Creating Hyperbrowser session...");
    session = await hyperBrowser.sessions.create({
      browserArgs: [
        "--auto-accept-this-tab-capture",
        "--use-fake-ui-for-media-stream",
        "--allow-running-insecure-content",
        "--disable-web-security"
      ],
      device: ["desktop"],
    });
    
    console.log("🔗 Connecting to Hyperbrowser...");
    browser = await puppeteer.connect({
      browserWSEndpoint: session.wsEndpoint,
    });

    const page = await browser.newPage();

    // Simple error monitoring - just console logs
    page.on('error', (error) => {
      console.error("💥 PAGE CRASH:", error.message);
    });

    page.on('pageerror', (error) => {
      console.error("💥 SCRIPT ERROR:", error.message);
    });

    // Log browser console messages
    page.on('console', (msg) => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️ BROWSER [${type.toUpperCase()}]: ${text}`);
    });

    // Grant permissions (using camera/microphone as display-capture isn't directly supported)
    const context = browser.defaultBrowserContext();
    await context.overridePermissions("https://google.com", [
      "camera",
      "microphone",
    ]);

    console.log("1️⃣ Navigating to Google...");
    await page.goto("https://google.com", { waitUntil: "domcontentloaded" });

    console.log("2️⃣ Starting screen sharing...");
    
    const result = await page.evaluate(async () => {
      try {
        console.log("🎥 Requesting screen sharing...");
        
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            frameRate: 22,
            width: window.width,
            height: window.height,
          },
          preferCurrentTab: true,
        });

        // Store globally for monitoring
        window.screenStream = screenStream;
        
        // Log stream events
        screenStream.addEventListener('inactive', () => {
          console.log("🔴 Stream became inactive");
        });

        screenStream.getVideoTracks().forEach((track, i) => {
          track.addEventListener('ended', () => {
            console.log(`🔴 Video track ${i} ended`);
          });
        });

        console.log("✅ Screen sharing started successfully");
        return { 
          success: true, 
          streamId: screenStream.id,
          active: screenStream.active,
          tracks: screenStream.getVideoTracks().length
        };
      } catch (error) {
        console.error("❌ Screen sharing failed:", error.message);
        return { success: false, error: error.message };
      }
    });

    if (!result.success) {
      console.error("Failed to start screen sharing:", result.error);
      await browser.close();
      if (session) await hyperBrowser.sessions.delete(session.id);
      return;
    }

    console.log("✅ Screen sharing active:", result);
    
    // Wait for screen sharing to stabilize
    await new Promise((resolve) => setTimeout(resolve, 2000));

    console.log("🛑 Stopping screen stream before navigation...");
    let streamStopped;
    try {
      streamStopped = await page.evaluate(() => {
        if (window.screenStream) {
          console.log("🛑 Stopping screen stream before navigation...");
          window.screenStream.getTracks().forEach((track) => {
            track.stop();
            console.log(`🛑 Stopped track: ${track.kind} (${track.readyState})`);
          });
          const wasActive = window.screenStream.active;
          window.screenStream = null;
          return { stopped: true, wasActive };
        }
        return { stopped: false, wasActive: false };
      });

      if (streamStopped.stopped) {
        console.log(
          `✅ Stream stopped successfully (was active: ${streamStopped.wasActive})`
        );
      } else {
        console.log("⚠️ No active stream to stop");
      }
    } catch (error) {
      console.error("💥 CRASH DETECTED: Stream stopping caused target to close!");
      console.error("Error details:", error.message);
      console.log("🔍 This confirms the screen sharing crash bug in Hyperbrowser!");
      
      // Try to check if browser is still alive
      try {
        const pages = await browser.pages();
        console.log(`📊 Browser status: ${pages.length} pages remaining`);
      } catch (browserError) {
        console.error("💥 Browser completely crashed:", browserError.message);
      }
      
      console.log("🏁 Test completed - crash successfully reproduced in Hyperbrowser!");
      await browser.close();
      if (session) await hyperBrowser.sessions.delete(session.id);
      return;
    }

    // Wait a moment after stopping
    await new Promise((resolve) => setTimeout(resolve, 1000));

    console.log("3️⃣ Clicking link to trigger navigation...");

    const linkClicked = await page.evaluate(() => {
      console.log("🔍 Looking for links to click...");

      // Find any clickable link
      const links = document.querySelectorAll(
        'a[href]:not([href="#"]):not([href=""])'
      );
      console.log(`Found ${links.length} links`);

      if (links.length > 0) {
        const link = links[0];
        console.log(`Clicking: ${link.href} (${link.textContent.trim()})`);
        link.click();
        return { success: true, href: link.href, text: link.textContent.trim() };
      }

      return { success: false };
    });

    if (linkClicked.success) {
      console.log(`🖱️ Clicked: ${linkClicked.href}`);
    } else {
      console.log("⚠️ No links found, navigating manually...");
      await page.goto("https://www.google.com/search?q=test");
    }

    console.log("⏳ Waiting to detect crash...");

    // Wait and monitor
    for (let i = 0; i < 10; i++) {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      try {
        const status = await page.evaluate(() => {
          if (window.screenStream) {
            return {
              active: window.screenStream.active,
              tracks: window.screenStream.getVideoTracks().map((t) => ({
                readyState: t.readyState,
                enabled: t.enabled,
              })),
            };
          }
          return null;
        });

        if (status) {
          console.log(`📊 Stream check ${i + 1}/10:`, status);
          if (!status.active) {
            console.log("🔴 Stream is no longer active!");
            break;
          }
        } else {
          console.log(
            `⚠️ Cannot access stream - possible crash at check ${i + 1}/10`
          );
          break;
        }
      } catch (error) {
        console.error(`💥 Error during check ${i + 1}/10:`, error.message);
        break;
      }
    }

    console.log(
      "🔍 Test completed. Check console output above for crash details."
    );
    console.log("Hyperbrowser session will be cleaned up.");

  } catch (error) {
    console.error("💥 HYPERBROWSER ERROR:", error.message);
    console.error("Full error:", error);
  } finally {
    // Clean up resources
    try {
      if (browser) {
        console.log("🧹 Closing browser...");
        await browser.close();
      }
    } catch (error) {
      console.error("Error closing browser:", error.message);
    }
    
    try {
      if (session && hyperBrowser) {
        console.log("🧹 Deleting Hyperbrowser session...");
        await hyperBrowser.sessions.delete(session.id);
      }
    } catch (error) {
      console.error("Error deleting session:", error.message);
    }
    
    console.log("✅ Cleanup completed");
  }
})();
