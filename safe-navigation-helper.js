const puppeteer = require("puppeteer");

/**
 * Safe Navigation Helper - Prevents crashes when using getDisplayMedia
 * 
 * This utility provides safe navigation methods that properly clean up
 * media streams before navigation to prevent Puppeteer crashes.
 */

class SafeNavigationHelper {
  constructor(page) {
    this.page = page;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    console.log("🔧 Initializing safe navigation helper...");

    // Inject stream management into the page
    await this.page.evaluateOnNewDocument(() => {
      if (window.safeNavHelper) return; // Already initialized

      window.safeNavHelper = {
        streams: new Map(),
        
        // Track a media stream
        trackStream: function(stream, name = 'unnamed') {
          const id = stream.id || Math.random().toString(36);
          this.streams.set(id, { stream, name, created: Date.now() });
          console.log(`📹 Tracking stream: ${name} (${id})`);
          
          // Auto-remove when stream ends
          stream.addEventListener('inactive', () => {
            this.streams.delete(id);
            console.log(`🔴 Stream ended: ${name} (${id})`);
          });
          
          return stream;
        },
        
        // Stop all tracked streams
        stopAllStreams: function() {
          console.log(`🛑 Stopping ${this.streams.size} tracked streams...`);
          
          for (const [id, { stream, name }] of this.streams) {
            try {
              stream.getTracks().forEach(track => {
                track.stop();
                console.log(`⏹️ Stopped ${track.kind} track from ${name}`);
              });
              this.streams.delete(id);
            } catch (error) {
              console.error(`❌ Error stopping stream ${name}:`, error);
            }
          }
          
          console.log("✅ All streams stopped");
        },
        
        // Get active streams info
        getActiveStreams: function() {
          return Array.from(this.streams.entries()).map(([id, { stream, name, created }]) => ({
            id,
            name,
            created,
            active: stream.active,
            trackCount: stream.getTracks().length
          }));
        }
      };

      // Override getDisplayMedia to auto-track
      if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
        const original = navigator.mediaDevices.getDisplayMedia;
        navigator.mediaDevices.getDisplayMedia = async function(constraints) {
          const stream = await original.call(this, constraints);
          return window.safeNavHelper.trackStream(stream, 'displayMedia');
        };
      }
    });

    this.isInitialized = true;
    console.log("✅ Safe navigation helper initialized");
  }

  // Safe navigation that stops streams first
  async safeGoto(url, options = {}) {
    await this.initialize();
    
    console.log(`🔗 Safe navigation to: ${url}`);
    
    // Stop all streams before navigation
    await this.page.evaluate(() => {
      if (window.safeNavHelper) {
        window.safeNavHelper.stopAllStreams();
      }
    });

    // Wait for cleanup
    await new Promise(resolve => setTimeout(resolve, 500));

    // Navigate safely
    try {
      await this.page.goto(url, { waitUntil: "domcontentloaded", ...options });
      console.log("✅ Safe navigation completed");
    } catch (error) {
      console.error("❌ Navigation failed:", error.message);
      throw error;
    }
  }

  // Safe page close
  async safeClose() {
    await this.initialize();
    
    console.log("🔒 Safe page close...");
    
    // Stop all streams before closing
    await this.page.evaluate(() => {
      if (window.safeNavHelper) {
        window.safeNavHelper.stopAllStreams();
      }
    });

    // Wait for cleanup
    await new Promise(resolve => setTimeout(resolve, 500));

    // Close safely
    try {
      await this.page.close();
      console.log("✅ Page closed safely");
    } catch (error) {
      console.error("❌ Page close failed:", error.message);
      throw error;
    }
  }

  // Get stream status
  async getStreamStatus() {
    await this.initialize();
    
    return await this.page.evaluate(() => {
      return window.safeNavHelper ? window.safeNavHelper.getActiveStreams() : [];
    });
  }

  // Manual stream cleanup
  async cleanupStreams() {
    await this.initialize();
    
    console.log("🧹 Manual stream cleanup...");
    await this.page.evaluate(() => {
      if (window.safeNavHelper) {
        window.safeNavHelper.stopAllStreams();
      }
    });
  }
}

// Demo usage
(async () => {
  console.log("🚀 Safe Navigation Helper Demo");
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: [
      "--auto-accept-this-tab-capture",
      "--use-fake-ui-for-media-stream",
      "--allow-running-insecure-content",
      "--disable-web-security"
    ],
  });

  const page = await browser.newPage();
  const navHelper = new SafeNavigationHelper(page);

  // Grant permissions
  const context = browser.defaultBrowserContext();
  await context.overridePermissions('https://google.com', ['display-capture']);

  console.log("1️⃣ Initial navigation...");
  await navHelper.safeGoto("https://google.com");

  console.log("2️⃣ Starting screen sharing...");
  const streamResult = await page.evaluate(async () => {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: { frameRate: 22 },
        preferCurrentTab: true,
      });
      
      console.log("✅ Screen sharing started");
      return { success: true, streamId: stream.id };
    } catch (error) {
      console.error("❌ Screen sharing failed:", error);
      return { success: false, error: error.message };
    }
  });

  if (!streamResult.success) {
    console.error("Failed to start screen sharing");
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active");

  // Check stream status
  const status = await navHelper.getStreamStatus();
  console.log("📊 Active streams:", status);

  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log("3️⃣ Testing safe navigation...");
  await navHelper.safeGoto("https://www.google.com/search?q=safe+navigation");

  console.log("4️⃣ Testing new stream after navigation...");
  const newStreamResult = await page.evaluate(async () => {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: { frameRate: 15 },
        preferCurrentTab: true,
      });
      
      return { success: true, streamId: stream.id };
    } catch (error) {
      return { success: false, error: error.message };
    }
  });

  console.log("New stream result:", newStreamResult);

  // Final status
  const finalStatus = await navHelper.getStreamStatus();
  console.log("📊 Final streams:", finalStatus);

  console.log("\n🎉 Demo completed successfully!");
  console.log("✅ No crashes occurred");
  console.log("✅ Safe navigation works");
  console.log("✅ Streams are properly managed");

  console.log("\n🔍 Browser remains open for manual testing");
  
  // Export the helper for manual use
  global.navHelper = navHelper;
  global.page = page;
  
  console.log("💡 Use global.navHelper.safeGoto(url) for safe navigation");
  console.log("💡 Use global.navHelper.cleanupStreams() to stop all streams");
})();

module.exports = SafeNavigationHelper;
