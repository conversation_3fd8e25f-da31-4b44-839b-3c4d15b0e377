// Input Focus Listener Script
// This script can be injected into pages to monitor input element focus events
// It works in both main documents and iframes, with dynamic content detection

// This runs in an isolated world, separate from the page's main world
console.log("🚀 [ISOLATED WORLD] Script loaded and initialized", document.URL);

// Add listeners to current document (works for both main document and iframes)
if (document.readyState === "loading") {
  console.log(
    `⏳ [ISOLATED WORLD] Document body not ready, waiting for DOM<PERSON>ontentLoaded to start observation`
  );
  document.addEventListener("DOMContentLoaded", () => {
    console.log(`✅ [ISOLATED WORLD] DOMContentLoaded fired `);
  });
} else {
  console.log(`✅ [ISOLATED WORLD] Document already loaded for`);
}

let mutationCount = 0;
const observer = new MutationObserver((mutations) => {
  mutationCount++;
  console.log(
    `🔄 [ISOLATED WORLD] Mutation batch #${mutationCount} detected with ${mutations.length} mutations`
  );

  mutations.forEach((mutation, mutIndex) => {
    console.log("mutation.type", mutation.type);
  });
});

console.log(`🎉 [ISOLATED WORLD] All initialization complete!`);
