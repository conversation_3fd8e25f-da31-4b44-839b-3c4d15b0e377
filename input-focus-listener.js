// Input Focus Listener Script
// This script can be injected into pages to monitor input element focus events
// It works in both main documents and iframes, with dynamic content detection

// This runs in an isolated world, separate from the page's main world
console.log("🚀 [ISOLATED WORLD] Script loaded and initialized");

// Function to add focus listeners to all input elements
function addFocusListeners(doc = document, context = "main") {
  console.log(
    `🔍 [ISOLATED WORLD] Adding focus listeners - Context: ${context}, Document: ${
      doc.URL || "N/A"
    }`
  );

  // Use getElementsByTagName for better performance
  const inputElements = doc.getElementsByTagName("input");
  const textareaElements = doc.getElementsByTagName("textarea");
  const selectElements = doc.getElementsByTagName("select");

  console.log(
    `📊 [ISOLATED WORLD] Found elements - Inputs: ${inputElements.length}, Textareas: ${textareaElements.length}, Selects: ${selectElements.length}`
  );

  // Convert HTMLCollections to arrays and combine
  const allInputs = [
    ...Array.from(inputElements),
    ...Array.from(textareaElements),
    ...Array.from(selectElements),
  ];

  let newListenersAdded = 0;
  let existingListeners = 0;

  allInputs.forEach((input, index) => {
    // Only add listener if not already added
    if (!input.hasAttribute("data-focus-listener-added")) {
      input.setAttribute("data-focus-listener-added", "true");
      newListenersAdded++;

      console.log(
        `➕ [ISOLATED WORLD] Adding listener to element ${index + 1}/${
          allInputs.length
        }:`,
        {
          tagName: input.tagName,
          type: input.type || "N/A",
          id: input.id || "N/A",
          className: input.className || "N/A",
          context: context,
        }
      );

      input.addEventListener("focus", (event) => {
        console.log(`🎯 [ISOLATED WORLD] ===== INPUT FOCUSED =====`);
        console.log(`📍 [ISOLATED WORLD] Element Details:`, {
          tagName: event.target.tagName,
          type: event.target.type || "N/A",
          id: event.target.id || "N/A",
          className: event.target.className || "N/A",
          placeholder: event.target.placeholder || "N/A",
          name: event.target.name || "N/A",
          value: event.target.value || "N/A",
          tabIndex: event.target.tabIndex,
          required: event.target.required,
          disabled: event.target.disabled,
          readonly: event.target.readOnly,
        });

        console.log(`⏰ [ISOLATED WORLD] Event Details:`, {
          timestamp: new Date().toISOString(),
          eventType: event.type,
          bubbles: event.bubbles,
          cancelable: event.cancelable,
          isTrusted: event.isTrusted,
        });

        console.log(`🎯 [ISOLATED WORLD] ===== END FOCUS EVENT =====`);
      });
    } else {
      existingListeners++;
    }
  });

  console.log(
    `✅ [ISOLATED WORLD] Listener summary - New: ${newListenersAdded}, Existing: ${existingListeners}, Total elements: ${allInputs.length}`
  );
}

// Detect if we're in an iframe or main document
const isIframe = window !== window.top;
const contextType = isIframe ? "iframe" : "main-document";

console.log(
  `🏁 [ISOLATED WORLD] Initial setup - Context: ${contextType}, Document ready state: ${document.readyState}, URL: ${document.URL}`
);

// Add listeners to current document (works for both main document and iframes)
if (document.readyState === "loading") {
  console.log(
    `⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation`
  );
  document.addEventListener("DOMContentLoaded", () => {
    console.log(
      `✅ [ISOLATED WORLD] DOMContentLoaded fired for ${contextType}, adding listeners`
    );
    addFocusListeners(document, `${contextType}-loaded`);
  });
} else {
  console.log(
    `✅ [ISOLATED WORLD] Document already loaded for ${contextType}, adding listeners immediately`
  );
  addFocusListeners(document, `${contextType}-immediate`);
}

let mutationCount = 0;
const observer = new MutationObserver((mutations) => {
  mutationCount++;
  console.log(
    `🔄 [ISOLATED WORLD] Mutation batch #${mutationCount} detected with ${mutations.length} mutations`
  );

  let hasNewInputs = false;
  let hasNewIframes = false;
  let inputsFound = 0;
  let iframesFound = 0;

  mutations.forEach((mutation, mutIndex) => {
    if (mutation.type === "childList") {
      console.log(
        `🔄 [ISOLATED WORLD] Processing mutation ${mutIndex + 1}/${
          mutations.length
        } - Added: ${mutation.addedNodes.length}, Removed: ${
          mutation.removedNodes.length
        }`
      );

      mutation.addedNodes.forEach((node, nodeIndex) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          console.log(
            `➕ [ISOLATED WORLD] Analyzing added node ${nodeIndex + 1}: ${
              node.tagName || "Unknown"
            }`
          );

          // Check if new node is an input or contains inputs
          if (node.matches && node.matches("input, textarea, select")) {
            console.log(
              `🎯 [ISOLATED WORLD] Direct input element added: ${node.tagName}`
            );
            hasNewInputs = true;
            inputsFound++;
          } else if (node.querySelectorAll) {
            const newInputs = node.querySelectorAll("input, textarea, select");
            if (newInputs.length > 0) {
              console.log(
                `🎯 [ISOLATED WORLD] Container with ${newInputs.length} input elements added`
              );
              hasNewInputs = true;
              inputsFound += newInputs.length;
            }
          }

          // Check for new iframes
          if (node.matches && node.matches("iframe")) {
            console.log(`🖼️ [ISOLATED WORLD] Direct iframe element added`);
            hasNewIframes = true;
            iframesFound++;
          } else if (node.querySelectorAll) {
            const newIframes = node.querySelectorAll("iframe");
            if (newIframes.length > 0) {
              console.log(
                `🖼️ [ISOLATED WORLD] Container with ${newIframes.length} iframe elements added`
              );
              hasNewIframes = true;
              iframesFound += newIframes.length;
            }
          }
        }
      });
    }
  });

  // Process findings
  if (hasNewInputs || hasNewIframes) {
    console.log(
      `🔄 [ISOLATED WORLD] Mutation summary - Inputs: ${inputsFound}, Iframes: ${iframesFound}`
    );

    if (hasNewInputs) {
      console.log(
        `🔄 [ISOLATED WORLD] Re-scanning document for new input elements...`
      );
      addFocusListeners(document, `${contextType}-mutation`);
    }
    if (hasNewIframes) {
      console.log(
        `🔄 [ISOLATED WORLD] Re-scanning document for new iframe elements...`
      );

      // Find all iframes in the document
      const allIframes = document.querySelectorAll("iframe");
      console.log(
        `🖼️ [ISOLATED WORLD] Found ${allIframes.length} total iframes in document`
      );

      allIframes.forEach((iframe, index) => {
        try {
          // Check if iframe is already processed
          if (iframe.hasAttribute("data-focus-listener-processed")) {
            console.log(
              `🖼️ [ISOLATED WORLD] Iframe ${
                index + 1
              } already processed, skipping`
            );
            return;
          }

          // Mark iframe as processed
          iframe.setAttribute("data-focus-listener-processed", "true");

          console.log(
            `🖼️ [ISOLATED WORLD] Processing iframe ${index + 1}/${
              allIframes.length
            }:`,
            {
              src: iframe.src || "N/A",
              id: iframe.id || "N/A",
              className: iframe.className || "N/A",
              name: iframe.name || "N/A",
            }
          );

          // Try to access iframe document (will fail for cross-origin iframes)
          let iframeDoc;
          try {
            iframeDoc =
              iframe.contentDocument || iframe.contentWindow?.document;
          } catch (crossOriginError) {
            console.log(
              `🚫 [ISOLATED WORLD] Cannot access iframe ${
                index + 1
              } document (cross-origin):`,
              crossOriginError.message
            );
            return;
          }

          if (!iframeDoc) {
            console.log(
              `⏳ [ISOLATED WORLD] Iframe ${
                index + 1
              } document not yet available, setting up load listener`
            );

            // Set up load event listener for when iframe content becomes available
            iframe.addEventListener("load", () => {
              console.log(
                `🔄 [ISOLATED WORLD] Iframe ${
                  index + 1
                } loaded, attempting to add listeners`
              );
              try {
                const loadedDoc =
                  iframe.contentDocument || iframe.contentWindow?.document;
                if (loadedDoc) {
                  console.log(
                    `✅ [ISOLATED WORLD] Successfully accessed loaded iframe ${
                      index + 1
                    } document`
                  );
                  addFocusListeners(loadedDoc, `iframe-${index + 1}-loaded`);
                } else {
                  console.log(
                    `🚫 [ISOLATED WORLD] Still cannot access iframe ${
                      index + 1
                    } document after load (likely cross-origin)`
                  );
                }
              } catch (loadError) {
                console.log(
                  `🚫 [ISOLATED WORLD] Error accessing iframe ${
                    index + 1
                  } document after load:`,
                  loadError.message
                );
              }
            });
            return;
          }

          // Iframe document is accessible, add listeners immediately
          console.log(
            `✅ [ISOLATED WORLD] Successfully accessed iframe ${
              index + 1
            } document, adding listeners`
          );
          addFocusListeners(iframeDoc, `iframe-${index + 1}-immediate`);
        } catch (error) {
          console.error(
            `❌ [ISOLATED WORLD] Error processing iframe ${index + 1}:`,
            error.message
          );
        }
      });
    }
  } else {
    console.log(
      `🔄 [ISOLATED WORLD] No relevant elements found in this mutation batch`
    );
  }
});

// Start observing
console.log(
  `👀 [ISOLATED WORLD] Starting mutation observer for ${contextType}...`
);
if (document.body) {
  console.log(
    `✅ [ISOLATED WORLD] Document body available for ${contextType}, starting observation immediately`
  );
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
} else {
  console.log(
    `⏳ [ISOLATED WORLD] Document body not ready, waiting for DOMContentLoaded to start observation`,
    document.URL
  );
  document.addEventListener("DOMContentLoaded", () => {
    console.log(
      `✅ [ISOLATED WORLD] DOMContentLoaded fired for ${contextType}, starting mutation observation`
    );
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  });
}
console.log(`🎉 [ISOLATED WORLD] All initialization complete!`);
