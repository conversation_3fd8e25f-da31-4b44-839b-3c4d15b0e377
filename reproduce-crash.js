const puppeteer = require("puppeteer");

(async () => {
  console.log("🚀 Reproducing screen sharing crash...");

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ["--auto-accept-this-tab-capture", "--disable-web-security"],
  });

  const page = await browser.newPage();

  console.log("1️⃣ Navigating to google.com...");
  await page.goto("https://google.com", { waitUntil: "domcontentloaded" });

  console.log("2️⃣ Starting screen sharing with getDisplayMedia...");

  // Your exact screen sharing code
  const result = await page.evaluate(async () => {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.width,
          height: window.height,
        },
        preferCurrentTab: true,
      });

      // Store stream globally for monitoring
      window.screenStream = screenStream;

      console.log("✅ Screen sharing started:", {
        id: screenStream.id,
        active: screenStream.active,
        tracks: screenStream.getVideoTracks().length,
      });

      return { success: true, streamId: screenStream.id };
    } catch (error) {
      console.error("❌ Screen sharing failed:", error);
      return { success: false, error: error.message };
    }
  });

  if (!result.success) {
    console.error("Failed to start screen sharing:", result.error);
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active");

  // Wait a moment for screen sharing to stabilize
  await new Promise((resolve) => setTimeout(resolve, 2000));

  console.log("3️⃣ Clicking on a link to trigger navigation...");

  // Click on any available link
  await page.evaluate(() => {
    // Find the first clickable link
    const links = document.querySelectorAll(
      'a[href]:not([href="#"]):not([href=""])'
    );
    if (links.length > 0) {
      const link = links[0];
      console.log(`Clicking on: ${link.href}`);
      link.click();
      return true;
    }
    return false;
  });

  // Monitor for crashes
  page.on("error", (error) => {
    console.error("💥 CRASH DETECTED - Page Error:", error.message);
  });

  page.on("pageerror", (error) => {
    console.error("💥 CRASH DETECTED - Script Error:", error.message);
  });

  // Wait and check if crash occurred
  console.log("⏳ Waiting to see if crash occurs...");
  await new Promise((resolve) => setTimeout(resolve, 5000));

  // Check if we can still access the page
  try {
    const streamStatus = await page.evaluate(() => {
      if (window.screenStream) {
        return {
          active: window.screenStream.active,
          tracks: window.screenStream.getVideoTracks().map((t) => t.readyState),
        };
      }
      return null;
    });

    if (streamStatus) {
      console.log("📊 Stream status after navigation:", streamStatus);
    } else {
      console.log("⚠️ Could not access stream - possible crash or navigation");
    }
  } catch (error) {
    console.error("💥 Error checking stream status:", error.message);
  }

  console.log("🔍 Test complete. Check browser for crash or errors.");

  // Keep browser open for inspection
  // Uncomment to auto-close: await browser.close();
})();
