import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";

(async () => {
  let browser;
  const useLocalBrowser = true;
  const launchNew = false;

  if (useLocalBrowser) {
    if (launchNew) {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [
          "--remote-debugging-port=9222",
          "--remote-allow-origins=*",
          "--no-first-run",
          "--auto-accept-this-tab-capture",
          "--no-default-browser-check",
        ],
      });
    } else {
      browser = await puppeteer.connect({
        browserWSEndpoint:
          "ws://localhost:9222/devtools/browser/dcb68ae3-f34a-4f33-b1a2-05b4b361f3de",
      });
    }
  } else {
    const hyperBrowser = new Hyperbrowser({
      apiKey: "hb_28aac10409666bbccf859a9b8804",
      timeout: 60000,
    });

    console.log("🔧 Creating Hyperbrowser session...");
    const session = await hyperBrowser.sessions.create({
      browserArgs: ["--auto-accept-this-tab-capture"],
      device: ["desktop"],
    });
    browser = await puppeteer.connect({
      browserWSEndpoint: session.wsEndpoint,
    });
  }

  // Create two pages - one for screen sharing, one for WebRTC peer
  const page1 = await browser.newPage();
  const page2 = await browser.newPage();

  // Setup error monitoring for both pages
  [page1, page2].forEach((page, index) => {
    page.on("error", (error) => {
      console.error(`💥 PAGE ${index + 1} CRASH:`, error.message);
    });

    page.on("pageerror", (error) => {
      console.error(`💥 PAGE ${index + 1} SCRIPT ERROR:`, error.message);
    });

    page.on("console", (msg) => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️ PAGE ${index + 1} [${type.toUpperCase()}]: ${text}`);
    });
  });

  console.log("1️⃣ Setting up WebRTC signaling server simulation...");

  // Navigate both pages
  await page1.goto("https://github.com/password_reset", {
    waitUntil: "domcontentloaded",
  });

  await page2.goto(
    "data:text/html,<html><body><h1>WebRTC Peer - Receiving Stream</h1><video id='remoteVideo' muted autoplay playsinline style='width:800px;height:600px;border:2px solid blue;'></video><div id='status'>Waiting for stream...</div></body></html>",
    {
      waitUntil: "domcontentloaded",
    }
  );

  console.log("2️⃣ Starting screen sharing on page 1 in isolated world...");
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Add isolated script to page 1
  await page1.addScriptTag({
    content: `
      // Create isolated namespace
      window.WebRTCTest = window.WebRTCTest || {};

      window.WebRTCTest.startScreenSharing = async function() {
    try {
      console.log("🎥 Requesting screen sharing...");
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: 22,
          width: window.innerWidth,
          height: window.innerHeight,
        },
        preferCurrentTab: true,
      });

      window.screenStream = screenStream;
      window.peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
      });

      // Add screen stream to peer connection
      screenStream.getTracks().forEach((track) => {
        console.log(`📡 Adding track to peer connection: ${track.kind}`);
        window.peerConnection.addTrack(track, screenStream);
      });

      // Setup peer connection event handlers
      window.peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          console.log("🧊 ICE candidate generated");
          window.iceCandidates = window.iceCandidates || [];
          window.iceCandidates.push(event.candidate);
        } else {
          console.log("🧊 ICE gathering complete");
          window.iceGatheringComplete = true;
        }
      };

      window.peerConnection.onconnectionstatechange = () => {
        console.log(
          `🔗 Connection state: ${window.peerConnection.connectionState}`
        );
      };

      window.peerConnection.oniceconnectionstatechange = () => {
        console.log(
          `🧊 ICE connection state: ${window.peerConnection.iceConnectionState}`
        );
      };

      // Create offer
      const offer = await window.peerConnection.createOffer();
      await window.peerConnection.setLocalDescription(offer);

      console.log("✅ Screen sharing + WebRTC setup completed");
      return {
        success: true,
        streamId: screenStream.id,
        active: screenStream.active,
        tracks: screenStream.getVideoTracks().length,
        offerSdpLength: offer.sdp.length, // Just log the length, not the content
      };
    } catch (error) {
      console.error("❌ Screen sharing + WebRTC failed:", error.message);
      return { success: false, error: error.message };
    }
  });

  if (!screenResult.success) {
    console.error(
      "Failed to start screen sharing + WebRTC:",
      screenResult.error
    );
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing + WebRTC active:", screenResult);

  console.log("3️⃣ Setting up WebRTC peer on page 2...");

  // Get the offer from page 1 and set up page 2 as answerer
  const offer = await page1.evaluate(() => {
    const desc = window.peerConnection.localDescription;
    console.log(
      "📤 Sending offer:",
      desc ? `${desc.type} (${desc.sdp.length} chars)` : "null"
    );
    return desc;
  });

  if (!offer) {
    console.error("❌ No offer available from page 1");
    await browser.close();
    return;
  }

  console.log(
    `📥 Received offer: ${offer.type} (${offer.sdp.length} characters)`
  );

  const answerResult = await page2.evaluate(async (offerData) => {
    try {
      console.log(
        "📥 Setting up peer connection with offer:",
        offerData ? `${offerData.type} (${offerData.sdp.length} chars)` : "null"
      );

      window.peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
      });

      // Setup event handlers
      window.peerConnection.ontrack = (event) => {
        console.log("📺 Received remote track:", event.track.kind);
        window.remoteStream = event.streams[0];

        // Display the stream in the video element
        const videoElement = document.getElementById("remoteVideo");
        const statusElement = document.getElementById("status");
        if (videoElement && window.remoteStream) {
          videoElement.srcObject = window.remoteStream;
          statusElement.textContent = `✅ Streaming ${
            event.track.kind
          } track - ${window.remoteStream.getTracks().length} tracks total`;
          console.log("🎬 Video element updated with remote stream");
        }
      };

      window.peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          console.log("🧊 Answer ICE candidate generated");
          window.answerCandidates = window.answerCandidates || [];
          window.answerCandidates.push(event.candidate);
        } else {
          console.log("🧊 Answer ICE gathering complete");
          window.answerIceGatheringComplete = true;
        }
      };

      // Validate offer data
      if (!offerData || !offerData.type || !offerData.sdp) {
        throw new Error("Invalid offer data received");
      }

      if (offerData.type !== "offer") {
        throw new Error(`Expected offer, got ${offerData.type}`);
      }

      console.log("✅ Offer validation passed, setting remote description...");

      // Set remote description and create answer
      await window.peerConnection.setRemoteDescription(offerData);
      console.log("✅ Remote description set successfully");

      const answer = await window.peerConnection.createAnswer();
      console.log("✅ Answer created successfully");

      await window.peerConnection.setLocalDescription(answer);
      console.log("✅ Local description set successfully");

      console.log("✅ WebRTC peer setup completed");
      return {
        success: true,
        answerSdpLength: answer.sdp.length,
      };
    } catch (error) {
      console.error("❌ WebRTC peer setup failed:", error.message);
      return { success: false, error: error.message };
    }
  }, offer);

  if (!answerResult.success) {
    console.error("Failed to setup WebRTC peer:", answerResult.error);
  } else {
    console.log("✅ WebRTC peer ready:", answerResult);

    // Complete the WebRTC handshake
    const answer = await page2.evaluate(
      () => window.peerConnection.localDescription
    );
    await page1.evaluate(async (answerData) => {
      await window.peerConnection.setRemoteDescription(answerData);
      console.log("🤝 WebRTC handshake completed");
    }, answer);

    // Wait for ICE gathering to complete on both sides
    console.log("🧊 Waiting for ICE gathering to complete...");

    // Wait for offer ICE gathering
    await page1.evaluate(async () => {
      while (!window.iceGatheringComplete) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
      console.log("🧊 Offer ICE gathering completed");
    });

    // Wait for answer ICE gathering
    await page2.evaluate(async () => {
      while (!window.answerIceGatheringComplete) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
      console.log("🧊 Answer ICE gathering completed");
    });

    // Exchange ICE candidates
    console.log("🧊 Exchanging ICE candidates...");

    // Get all ICE candidates from page 1 and add them to page 2
    const offerCandidates = await page1.evaluate(
      () => window.iceCandidates || []
    );
    console.log(`🧊 Found ${offerCandidates.length} offer candidates`);

    if (offerCandidates.length > 0) {
      await page2.evaluate(async (candidates) => {
        for (const candidate of candidates) {
          try {
            await window.peerConnection.addIceCandidate(candidate);
            console.log("🧊 Added offer ICE candidate");
          } catch (error) {
            console.error("❌ Failed to add offer candidate:", error.message);
          }
        }
      }, offerCandidates);
    }

    // Get all ICE candidates from page 2 and add them to page 1
    const answerCandidates = await page2.evaluate(
      () => window.answerCandidates || []
    );
    console.log(`🧊 Found ${answerCandidates.length} answer candidates`);

    if (answerCandidates.length > 0) {
      await page1.evaluate(async (candidates) => {
        for (const candidate of candidates) {
          try {
            await window.peerConnection.addIceCandidate(candidate);
            console.log("🧊 Added answer ICE candidate");
          } catch (error) {
            console.error("❌ Failed to add answer candidate:", error.message);
          }
        }
      }, answerCandidates);
    }
  }

  // Wait for WebRTC connection to establish
  console.log("⏳ Waiting for WebRTC connection to establish...");

  // Monitor connection establishment
  for (let i = 0; i < 20; i++) {
    await new Promise((resolve) => setTimeout(resolve, 500));

    const connectionState = await page1.evaluate(() => {
      return {
        connectionState: window.peerConnection.connectionState,
        iceConnectionState: window.peerConnection.iceConnectionState,
        streamActive: window.screenStream.active,
      };
    });

    const page2State = await page2.evaluate(() => {
      return {
        connectionState: window.peerConnection.connectionState,
        iceConnectionState: window.peerConnection.iceConnectionState,
        hasRemoteStream: !!window.remoteStream,
        remoteStreamTracks: window.remoteStream
          ? window.remoteStream.getTracks().length
          : 0,
      };
    });

    console.log(`🔗 Connection attempt ${i + 1}/20:`);
    console.log(
      `   Page 1: ${connectionState.connectionState}/${connectionState.iceConnectionState}`
    );
    console.log(
      `   Page 2: ${page2State.connectionState}/${page2State.iceConnectionState}, Stream: ${page2State.hasRemoteStream}, Tracks: ${page2State.remoteStreamTracks}`
    );

    if (
      connectionState.connectionState === "connected" &&
      page2State.hasRemoteStream
    ) {
      console.log("✅ WebRTC connection established successfully!");
      break;
    }

    if (
      connectionState.connectionState === "failed" ||
      page2State.connectionState === "failed"
    ) {
      console.log("❌ WebRTC connection failed!");
      break;
    }
  }

  console.log("4️⃣ Final WebRTC connection state...");
  const finalConnectionState = await page1.evaluate(() => {
    return {
      connectionState: window.peerConnection.connectionState,
      iceConnectionState: window.peerConnection.iceConnectionState,
      streamActive: window.screenStream.active,
    };
  });
  console.log("🔗 Final WebRTC Status:", finalConnectionState);

  // Check if video is actually streaming on page 2
  const streamingStatus = await page2.evaluate(() => {
    const video = document.getElementById("remoteVideo");
    const status = document.getElementById("status");
    return {
      videoSrc: video.srcObject ? "Stream attached" : "No stream",
      videoPlaying: !video.paused && !video.ended && video.readyState > 2,
      statusText: status.textContent,
      streamTracks: window.remoteStream
        ? window.remoteStream.getTracks().length
        : 0,
    };
  });
  console.log("📺 Video streaming status:", streamingStatus);

  console.log("🎬 Letting video stream for 15 seconds to verify streaming...");

  // Stream for 15 seconds with periodic status updates
  for (let i = 1; i <= 15; i++) {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    if (i % 3 === 0) {
      // Update every 3 seconds
      const status = await page2.evaluate(() => {
        const video = document.getElementById("remoteVideo");
        return {
          currentTime: video.currentTime.toFixed(1),
          videoWidth: video.videoWidth,
          videoHeight: video.videoHeight,
          playing: !video.paused && !video.ended,
        };
      });
      console.log(
        `📊 Streaming ${i}/15s - Video: ${status.videoWidth}x${status.videoHeight}, Time: ${status.currentTime}s, Playing: ${status.playing}`
      );
    }
  }

  console.log("✅ 15-second streaming period completed!");

  console.log("5️⃣ Testing navigation with active WebRTC + screen sharing...");

  // Test navigation while WebRTC is active
  console.log("🛑 Attempting navigation while WebRTC is active...");

  try {
    const linkClicked = await page1.evaluate(() => {
      console.log("🔍 Looking for header-logo link to click...");

      const headerLogoLink = document.querySelector("a.header-logo");
      if (headerLogoLink) {
        console.log(`Clicking header logo: ${headerLogoLink.href}`);
        headerLogoLink.click();
        return { success: true, href: headerLogoLink.href };
      }

      // Fallback
      const links = document.querySelectorAll(
        'a[href]:not([href="#"]):not([href=""])'
      );
      if (links.length > 0) {
        const link = links[0];
        console.log(`Clicking fallback: ${link.href}`);
        link.click();
        return { success: true, href: link.href };
      }

      return { success: false };
    });

    if (linkClicked.success) {
      console.log(`🖱️ Clicked: ${linkClicked.href}`);
    } else {
      console.log("⚠️ No links found, navigating manually...");
      await page1.goto("https://www.google.com/search?q=webrtc+test");
    }

    console.log("⏳ Monitoring for crashes during navigation...");

    // Monitor for crashes
    for (let i = 0; i < 15; i++) {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      try {
        const status = await page1.evaluate(() => {
          const streamStatus = window.screenStream
            ? {
                active: window.screenStream.active,
                tracks: window.screenStream.getVideoTracks().length,
              }
            : null;

          const rtcStatus = window.peerConnection
            ? {
                connectionState: window.peerConnection.connectionState,
                iceConnectionState: window.peerConnection.iceConnectionState,
              }
            : null;

          return { stream: streamStatus, rtc: rtcStatus };
        });

        console.log(`📊 Status check ${i + 1}/15:`, status);

        if (status.stream && !status.stream.active) {
          console.log("🔴 Screen stream is no longer active!");
        }

        if (status.rtc && status.rtc.connectionState === "failed") {
          console.log("🔴 WebRTC connection failed!");
        }
      } catch (error) {
        console.error(`💥 Error during check ${i + 1}/15:`, error.message);
        console.log("🔍 This might indicate a crash!");
        break;
      }
    }
  } catch (error) {
    console.error("💥 CRASH DETECTED during navigation:", error.message);
    console.log("🔍 WebRTC + Screen sharing crash confirmed!");
  }

  console.log("🔍 WebRTC crash test completed.");
  console.log("📊 Check console output above for crash analysis.");
  console.log("🌐 Both browser pages will remain open for manual inspection.");

  // Keep browser open for inspection
  // Uncomment to auto-close: await browser.close();
})();
