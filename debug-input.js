import { createTarget, CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function debugInputDispatching() {
  console.log("=== Debugging Input Dispatching ===");
  
  try {
    // Create target for a page with forms
    const url = "https://httpbin.org/forms/post";
    console.log(`Creating target for form page: ${url}`);
    const targetInfo = await createTarget(url);
    console.log("Target info:", targetInfo);
    
    const cdpInstance = new CDP(targetInfo);

    // Enable necessary domains
    await cdpInstance.Runtime.enable();
    await cdpInstance.Page.enable();
    console.log("Domains enabled");

    // Wait for page to load
    console.log("Waiting for page to load...");
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // Test basic JavaScript evaluation
    console.log("\n--- Testing Basic JavaScript ---");
    const basicTest = await cdpInstance.Runtime.evaluate({
      expression: "2 + 2",
    });
    console.log("Basic math result:", basicTest);

    // Test document access
    console.log("\n--- Testing Document Access ---");
    const documentTest = await cdpInstance.Runtime.evaluate({
      expression: "document.title",
    });
    console.log("Document title result:", documentTest);

    // Test simple DOM query
    console.log("\n--- Testing DOM Query ---");
    const domTest = await cdpInstance.Runtime.evaluate({
      expression: "document.querySelectorAll('*').length",
    });
    console.log("DOM elements count:", domTest);

    // Test input elements specifically
    console.log("\n--- Testing Input Elements ---");
    const inputTest = await cdpInstance.Runtime.evaluate({
      expression: "document.querySelectorAll('input').length",
    });
    console.log("Input elements count:", inputTest);

    // Test form elements
    console.log("\n--- Testing Form Elements ---");
    const formTest = await cdpInstance.Runtime.evaluate({
      expression: `
        try {
          const inputs = document.querySelectorAll('input');
          const result = {
            inputCount: inputs.length,
            inputs: Array.from(inputs).map(input => ({
              name: input.name,
              type: input.type,
              id: input.id
            }))
          };
          result;
        } catch (e) {
          ({ error: e.message });
        }
      `,
    });
    console.log("Form elements result:", formTest);

    // Test page readiness
    console.log("\n--- Testing Page Readiness ---");
    const readyTest = await cdpInstance.Runtime.evaluate({
      expression: "document.readyState",
    });
    console.log("Document ready state:", readyTest);

    // Try input dispatching on a simple coordinate
    console.log("\n--- Testing Simple Mouse Click ---");
    try {
      await cdpInstance.Input.dispatchMouseEvent({
        type: "mousePressed",
        x: 100,
        y: 100,
        button: "left",
        clickCount: 1,
      });
      await cdpInstance.Input.dispatchMouseEvent({
        type: "mouseReleased",
        x: 100,
        y: 100,
        button: "left",
        clickCount: 1,
      });
      console.log("Simple mouse click successful");
    } catch (error) {
      console.log("Mouse click error:", error.message);
    }

    // Try keyboard input
    console.log("\n--- Testing Simple Keyboard Input ---");
    try {
      await cdpInstance.Input.dispatchKeyEvent({
        type: "keyDown",
        text: "a",
      });
      await cdpInstance.Input.dispatchKeyEvent({
        type: "keyUp",
        text: "a",
      });
      console.log("Simple keyboard input successful");
    } catch (error) {
      console.log("Keyboard input error:", error.message);
    }

    console.log("\nDebugging completed");
  } catch (error) {
    console.error("Error in debugging:", error);
  }
}

debugInputDispatching().catch(console.error);
