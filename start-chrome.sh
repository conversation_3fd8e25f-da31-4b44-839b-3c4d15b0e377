#!/bin/bash

# <PERSON>ript to start Chrome with remote debugging enabled
# This is required for the simple-cdp script to work

echo "Starting Chrome with remote debugging on port 9222..."

# Try different Chrome executable names/paths
CHROME_PATHS=(
    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    "/Applications/Chromium.app/Contents/MacOS/Chromium"
    "google-chrome"
    "chromium"
    "chrome"
)

CHROME_EXEC=""

# Find available Chrome executable
for path in "${CHROME_PATHS[@]}"; do
    if command -v "$path" &> /dev/null || [ -f "$path" ]; then
        CHROME_EXEC="$path"
        echo "Found Chrome at: $CHROME_EXEC"
        break
    fi
done

if [ -z "$CHROME_EXEC" ]; then
    echo "Error: Chrome/Chromium not found!"
    echo "Please install Chrome or update the CHROME_PATHS in this script."
    exit 1
fi

# Chrome flags for remote debugging
CHROME_FLAGS=(
    --remote-debugging-port=9222
    --no-first-run
    --no-default-browser-check
    --disable-background-timer-throttling
    --disable-backgrounding-occluded-windows
    --disable-renderer-backgrounding
    --disable-features=TranslateUI
    --disable-ipc-flooding-protection
    --user-data-dir=/tmp/chrome-debug-profile
)

echo "Chrome flags: ${CHROME_FLAGS[*]}"
echo ""
echo "Chrome will start with remote debugging enabled."
echo "You can now run: node simple-cdp-script.js"
echo ""
echo "To stop Chrome, press Ctrl+C in this terminal."
echo ""

# Start Chrome
"$CHROME_EXEC" "${CHROME_FLAGS[@]}"
