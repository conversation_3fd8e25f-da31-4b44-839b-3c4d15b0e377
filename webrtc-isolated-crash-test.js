import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";

(async () => {
  let browser;
  const useLocalBrowser = true;
  const launchNew = false;
  
  if (useLocalBrowser) {
    if (launchNew) {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [
          "--remote-debugging-port=9222",
          "--remote-allow-origins=*",
          "--no-first-run",
          "--auto-accept-this-tab-capture",
          "--no-default-browser-check",
        ],
      });
    } else {
      browser = await puppeteer.connect({
        browserWSEndpoint:
          "ws://localhost:9222/devtools/browser/5b0b74aa-b6f3-447d-8343-a9a0a53e1f06",
      });
    }
  } else {
    const hyperBrowser = new Hyperbrowser({
      apiKey: "hb_28aac10409666bbccf859a9b8804",
      timeout: 60000,
    });

    console.log("🔧 Creating Hyperbrowser session...");
    const session = await hyperBrowser.sessions.create({
      browserArgs: ["--auto-accept-this-tab-capture"],
      device: ["desktop"],
    });
    browser = await puppeteer.connect({
      browserWSEndpoint: session.wsEndpoint,
    });
  }

  // Create two pages - one for screen sharing, one for WebRTC peer
  const page1 = await browser.newPage();
  const page2 = await browser.newPage();

  // Setup error monitoring for both pages
  [page1, page2].forEach((page, index) => {
    page.on("error", (error) => {
      console.error(`💥 PAGE ${index + 1} CRASH:`, error.message);
    });

    page.on("pageerror", (error) => {
      console.error(`💥 PAGE ${index + 1} SCRIPT ERROR:`, error.message);
    });

    page.on("console", (msg) => {
      const type = msg.type();
      const text = msg.text();
      console.log(`🖥️ PAGE ${index + 1} [${type.toUpperCase()}]: ${text}`);
    });
  });

  console.log("1️⃣ Setting up isolated WebRTC environment...");
  
  // Navigate both pages
  await page1.goto("https://github.com/password_reset", {
    waitUntil: "domcontentloaded",
  });
  
  await page2.goto("data:text/html,<html><body><h1>WebRTC Peer - Receiving Stream</h1><video id='remoteVideo' autoplay playsinline style='width:800px;height:600px;border:2px solid blue;'></video><div id='status'>Waiting for stream...</div></body></html>", {
    waitUntil: "domcontentloaded",
  });

  // Inject isolated WebRTC scripts into both pages
  console.log("🌍 Injecting isolated WebRTC scripts...");
  
  // Page 1: Screen sharing and offer creation
  await page1.addScriptTag({
    content: `
      // Create isolated namespace
      window.WebRTCTest = {
        screenStream: null,
        peerConnection: null,
        iceCandidates: [],
        iceGatheringComplete: false,
        
        async startScreenSharing() {
          try {
            console.log("🎥 Requesting screen sharing in isolated context...");
            this.screenStream = await navigator.mediaDevices.getDisplayMedia({
              video: {
                frameRate: 22,
                width: window.innerWidth,
                height: window.innerHeight,
              },
              preferCurrentTab: true,
            });
            
            this.peerConnection = new RTCPeerConnection({
              iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
            });

            // Add screen stream to peer connection
            this.screenStream.getTracks().forEach(track => {
              console.log('📡 Adding track to peer connection:', track.kind);
              this.peerConnection.addTrack(track, this.screenStream);
            });

            // Setup peer connection event handlers
            this.peerConnection.onicecandidate = (event) => {
              if (event.candidate) {
                console.log("🧊 ICE candidate generated");
                this.iceCandidates.push(event.candidate);
              } else {
                console.log("🧊 ICE gathering complete");
                this.iceGatheringComplete = true;
              }
            };

            this.peerConnection.onconnectionstatechange = () => {
              console.log('🔗 Connection state:', this.peerConnection.connectionState);
            };

            this.peerConnection.oniceconnectionstatechange = () => {
              console.log('🧊 ICE connection state:', this.peerConnection.iceConnectionState);
            };

            // Create offer
            const offer = await this.peerConnection.createOffer();
            await this.peerConnection.setLocalDescription(offer);

            console.log("✅ Screen sharing + WebRTC setup completed in isolated context");
            return {
              success: true,
              streamId: this.screenStream.id,
              active: this.screenStream.active,
              tracks: this.screenStream.getVideoTracks().length,
            };
          } catch (error) {
            console.error("❌ Screen sharing + WebRTC failed:", error.message);
            return { success: false, error: error.message };
          }
        },
        
        getOffer() {
          return this.peerConnection ? this.peerConnection.localDescription : null;
        },
        
        getIceCandidates() {
          return this.iceCandidates;
        },
        
        isIceGatheringComplete() {
          return this.iceGatheringComplete;
        },
        
        async setRemoteDescription(answer) {
          if (this.peerConnection) {
            await this.peerConnection.setRemoteDescription(answer);
            console.log("🤝 WebRTC handshake completed in isolated context");
          }
        },
        
        async addIceCandidate(candidate) {
          if (this.peerConnection) {
            await this.peerConnection.addIceCandidate(candidate);
            console.log("🧊 Added ICE candidate in isolated context");
          }
        },
        
        getConnectionState() {
          return this.peerConnection ? {
            connectionState: this.peerConnection.connectionState,
            iceConnectionState: this.peerConnection.iceConnectionState,
            streamActive: this.screenStream ? this.screenStream.active : false,
          } : null;
        }
      };
    `
  });

  // Page 2: Answer creation and video display
  await page2.addScriptTag({
    content: `
      // Create isolated namespace
      window.WebRTCTest = {
        peerConnection: null,
        remoteStream: null,
        answerCandidates: [],
        answerIceGatheringComplete: false,
        
        async setupPeerConnection(offerData) {
          try {
            console.log("📥 Setting up peer connection with offer in isolated context");
            
            this.peerConnection = new RTCPeerConnection({
              iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
            });

            // Setup event handlers
            this.peerConnection.ontrack = (event) => {
              console.log("📺 Received remote track:", event.track.kind);
              this.remoteStream = event.streams[0];
              
              // Display the stream in the video element
              const videoElement = document.getElementById('remoteVideo');
              const statusElement = document.getElementById('status');
              if (videoElement && this.remoteStream) {
                videoElement.srcObject = this.remoteStream;
                statusElement.textContent = '✅ Streaming ' + event.track.kind + ' track - ' + this.remoteStream.getTracks().length + ' tracks total';
                console.log("🎬 Video element updated with remote stream in isolated context");
              }
            };

            this.peerConnection.onicecandidate = (event) => {
              if (event.candidate) {
                console.log("🧊 Answer ICE candidate generated");
                this.answerCandidates.push(event.candidate);
              } else {
                console.log("🧊 Answer ICE gathering complete");
                this.answerIceGatheringComplete = true;
              }
            };

            // Validate and set remote description
            if (!offerData || !offerData.type || !offerData.sdp) {
              throw new Error("Invalid offer data received");
            }
            
            if (offerData.type !== 'offer') {
              throw new Error('Expected offer, got ' + offerData.type);
            }
            
            console.log("✅ Offer validation passed, setting remote description...");
            
            await this.peerConnection.setRemoteDescription(offerData);
            console.log("✅ Remote description set successfully");
            
            const answer = await this.peerConnection.createAnswer();
            console.log("✅ Answer created successfully");
            
            await this.peerConnection.setLocalDescription(answer);
            console.log("✅ Local description set successfully");

            console.log("✅ WebRTC peer setup completed in isolated context");
            return {
              success: true,
              answerSdpLength: answer.sdp.length,
            };
          } catch (error) {
            console.error("❌ WebRTC peer setup failed:", error.message);
            return { success: false, error: error.message };
          }
        },
        
        getAnswer() {
          return this.peerConnection ? this.peerConnection.localDescription : null;
        },
        
        getAnswerCandidates() {
          return this.answerCandidates;
        },
        
        isAnswerIceGatheringComplete() {
          return this.answerIceGatheringComplete;
        },
        
        async addIceCandidate(candidate) {
          if (this.peerConnection) {
            await this.peerConnection.addIceCandidate(candidate);
            console.log("🧊 Added offer ICE candidate in isolated context");
          }
        },
        
        getConnectionState() {
          return this.peerConnection ? {
            connectionState: this.peerConnection.connectionState,
            iceConnectionState: this.peerConnection.iceConnectionState,
            hasRemoteStream: !!this.remoteStream,
            remoteStreamTracks: this.remoteStream ? this.remoteStream.getTracks().length : 0
          } : null;
        },
        
        getVideoStatus() {
          const video = document.getElementById('remoteVideo');
          const status = document.getElementById('status');
          return {
            videoSrc: video.srcObject ? 'Stream attached' : 'No stream',
            videoPlaying: !video.paused && !video.ended && video.readyState > 2,
            statusText: status.textContent,
            streamTracks: this.remoteStream ? this.remoteStream.getTracks().length : 0,
            currentTime: video.currentTime.toFixed(1),
            videoWidth: video.videoWidth,
            videoHeight: video.videoHeight,
            playing: !video.paused && !video.ended
          };
        }
      };
    `
  });

  console.log("2️⃣ Starting screen sharing in isolated context...");
  await new Promise((resolve) => setTimeout(resolve, 2000));

  const screenResult = await page1.evaluate(() => {
    return window.WebRTCTest.startScreenSharing();
  });

  if (!screenResult.success) {
    console.error("Failed to start screen sharing:", screenResult.error);
    await browser.close();
    return;
  }

  console.log("✅ Screen sharing active in isolated context:", screenResult);

  console.log("3️⃣ Setting up WebRTC peer in isolated context...");
  
  // Get the offer from page 1
  const offer = await page1.evaluate(() => {
    const desc = window.WebRTCTest.getOffer();
    console.log("📤 Sending offer:", desc ? desc.type + ' (' + desc.sdp.length + ' chars)' : 'null');
    return desc;
  });

  if (!offer) {
    console.error("❌ No offer available from page 1");
    await browser.close();
    return;
  }

  console.log(`📥 Received offer: ${offer.type} (${offer.sdp.length} characters)`);

  const answerResult = await page2.evaluate((offerData) => {
    return window.WebRTCTest.setupPeerConnection(offerData);
  }, offer);

  if (!answerResult.success) {
    console.error("Failed to setup WebRTC peer:", answerResult.error);
    await browser.close();
    return;
  } else {
    console.log("✅ WebRTC peer ready in isolated context:", answerResult);
    
    // Complete the WebRTC handshake
    const answer = await page2.evaluate(() => window.WebRTCTest.getAnswer());
    await page1.evaluate((answerData) => {
      return window.WebRTCTest.setRemoteDescription(answerData);
    }, answer);

    // Wait for ICE gathering to complete on both sides
    console.log("🧊 Waiting for ICE gathering to complete in isolated contexts...");
    
    await page1.evaluate(async () => {
      while (!window.WebRTCTest.isIceGatheringComplete()) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      console.log("🧊 Offer ICE gathering completed in isolated context");
    });
    
    await page2.evaluate(async () => {
      while (!window.WebRTCTest.isAnswerIceGatheringComplete()) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      console.log("🧊 Answer ICE gathering completed in isolated context");
    });

    // Exchange ICE candidates
    console.log("🧊 Exchanging ICE candidates in isolated contexts...");
    
    const offerCandidates = await page1.evaluate(() => window.WebRTCTest.getIceCandidates());
    console.log(`🧊 Found ${offerCandidates.length} offer candidates`);
    
    if (offerCandidates.length > 0) {
      await page2.evaluate(async (candidates) => {
        for (const candidate of candidates) {
          try {
            await window.WebRTCTest.addIceCandidate(candidate);
          } catch (error) {
            console.error("❌ Failed to add offer candidate:", error.message);
          }
        }
      }, offerCandidates);
    }

    const answerCandidates = await page2.evaluate(() => window.WebRTCTest.getAnswerCandidates());
    console.log(`🧊 Found ${answerCandidates.length} answer candidates`);
    
    if (answerCandidates.length > 0) {
      await page1.evaluate(async (candidates) => {
        for (const candidate of candidates) {
          try {
            await window.WebRTCTest.addIceCandidate(candidate);
          } catch (error) {
            console.error("❌ Failed to add answer candidate:", error.message);
          }
        }
      }, answerCandidates);
    }
  }

  // Wait for WebRTC connection to establish
  console.log("⏳ Waiting for WebRTC connection to establish in isolated contexts...");
  
  for (let i = 0; i < 20; i++) {
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    const connectionState = await page1.evaluate(() => window.WebRTCTest.getConnectionState());
    const page2State = await page2.evaluate(() => window.WebRTCTest.getConnectionState());
    
    console.log(`🔗 Connection attempt ${i + 1}/20:`);
    console.log(`   Page 1: ${connectionState.connectionState}/${connectionState.iceConnectionState}`);
    console.log(`   Page 2: ${page2State.connectionState}/${page2State.iceConnectionState}, Stream: ${page2State.hasRemoteStream}, Tracks: ${page2State.remoteStreamTracks}`);
    
    if (connectionState.connectionState === "connected" && page2State.hasRemoteStream) {
      console.log("✅ WebRTC connection established successfully in isolated contexts!");
      break;
    }
    
    if (connectionState.connectionState === "failed" || page2State.connectionState === "failed") {
      console.log("❌ WebRTC connection failed in isolated contexts!");
      break;
    }
  }

  console.log("4️⃣ Final WebRTC connection state in isolated contexts...");
  const finalConnectionState = await page1.evaluate(() => window.WebRTCTest.getConnectionState());
  console.log("🔗 Final WebRTC Status:", finalConnectionState);

  // Check if video is actually streaming on page 2
  const streamingStatus = await page2.evaluate(() => window.WebRTCTest.getVideoStatus());
  console.log("📺 Video streaming status in isolated context:", streamingStatus);

  console.log("🎬 Letting video stream for 15 seconds to verify streaming in isolated context...");
  
  // Stream for 15 seconds with periodic status updates
  for (let i = 1; i <= 15; i++) {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    
    if (i % 3 === 0) {
      const status = await page2.evaluate(() => window.WebRTCTest.getVideoStatus());
      console.log(`📊 Streaming ${i}/15s - Video: ${status.videoWidth}x${status.videoHeight}, Time: ${status.currentTime}s, Playing: ${status.playing}`);
    }
  }

  console.log("✅ 15-second streaming period completed in isolated context!");

  console.log("5️⃣ Testing navigation with active WebRTC + screen sharing in isolated context...");
  
  // Test navigation while WebRTC is active
  console.log("🛑 Attempting navigation while WebRTC is active in isolated context...");
  
  try {
    const linkClicked = await page1.evaluate(() => {
      console.log("🔍 Looking for header-logo link to click...");
      
      const headerLogoLink = document.querySelector("a.header-logo");
      if (headerLogoLink) {
        console.log(`Clicking header logo: ${headerLogoLink.href}`);
        headerLogoLink.click();
        return { success: true, href: headerLogoLink.href };
      }
      
      const links = document.querySelectorAll('a[href]:not([href="#"]):not([href=""])');
      if (links.length > 0) {
        const link = links[0];
        console.log(`Clicking fallback: ${link.href}`);
        link.click();
        return { success: true, href: link.href };
      }
      
      return { success: false };
    });

    if (linkClicked.success) {
      console.log(`🖱️ Clicked: ${linkClicked.href}`);
    } else {
      console.log("⚠️ No links found, navigating manually...");
      await page1.goto("https://www.google.com/search?q=webrtc+test");
    }

    console.log("⏳ Monitoring for crashes during navigation in isolated context...");
    
    // Monitor for crashes
    for (let i = 0; i < 15; i++) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      try {
        const status = await page1.evaluate(() => window.WebRTCTest.getConnectionState());
        const page2Status = await page2.evaluate(() => window.WebRTCTest.getConnectionState());
        
        console.log(`📊 Status check ${i + 1}/15:`, { page1: status, page2: page2Status });
        
        if (status && !status.streamActive) {
          console.log("🔴 Screen stream is no longer active in isolated context!");
        }
        
        if (status && status.connectionState === 'failed') {
          console.log("🔴 WebRTC connection failed in isolated context!");
        }
        
      } catch (error) {
        console.error(`💥 Error during check ${i + 1}/15:`, error.message);
        console.log("🔍 This might indicate a crash in isolated context!");
        break;
      }
    }
    
  } catch (error) {
    console.error("💥 CRASH DETECTED during navigation in isolated context:", error.message);
    console.log("🔍 WebRTC + Screen sharing crash confirmed in isolated context!");
  }

  console.log("🔍 WebRTC crash test completed in isolated context.");
  console.log("📊 Check console output above for crash analysis.");
  console.log("🌐 Both browser pages will remain open for manual inspection.");
  
  // Keep browser open for inspection
  // Uncomment to auto-close: await browser.close();
})();
